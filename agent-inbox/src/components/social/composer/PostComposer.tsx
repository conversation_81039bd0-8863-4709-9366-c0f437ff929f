"use client";

import React, { useState } from 'react';
import { 
  Send, 
  Calendar, 
  Image, 
  Hash, 
  AtSign, 
  X,
  Twitter,
  Facebook,
  Instagram,
  Linkedin,
  Clock,
  Zap
} from 'lucide-react';
import { useSocialStore } from '@/store/social/socialStore';

interface PostComposerProps {
  className?: string;
  onClose?: () => void;
}

const platformIcons = {
  twitter: Twitter,
  facebook: Facebook,
  instagram: Instagram,
  linkedin: Linkedin,
};

const platformColors = {
  twitter: 'text-blue-500 border-blue-200',
  facebook: 'text-blue-600 border-blue-200',
  instagram: 'text-pink-500 border-pink-200',
  linkedin: 'text-blue-700 border-blue-200',
};

const platformLimits = {
  twitter: 280,
  facebook: 63206,
  instagram: 2200,
  linkedin: 3000,
};

export function PostComposer({ className, onClose }: PostComposerProps) {
  const [content, setContent] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [isScheduled, setIsScheduled] = useState(false);

  const {
    connectedPlatforms,
    createPost,
    schedulePost,
    publishPost,
    isPublishing,
    settings,
  } = useSocialStore();

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms(prev =>
      prev.includes(platformId)
        ? prev.filter(id => id !== platformId)
        : [...prev, platformId]
    );
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setMediaFiles(prev => [...prev, ...files]);
  };

  const removeMediaFile = (index: number) => {
    setMediaFiles(prev => prev.filter((_, i) => i !== index));
  };

  const getCharacterLimit = () => {
    if (selectedPlatforms.length === 0) return 280; // Default to Twitter limit
    
    const limits = selectedPlatforms.map(platformId => {
      const platform = connectedPlatforms.find(p => p.id === platformId);
      return platform ? platformLimits[platform.name] : 280;
    });
    
    return Math.min(...limits);
  };

  const getHashtags = () => {
    return content.match(/#\w+/g) || [];
  };

  const getMentions = () => {
    return content.match(/@\w+/g) || [];
  };

  const handlePublish = async () => {
    if (!content.trim() || selectedPlatforms.length === 0) return;

    const postId = createPost(content, selectedPlatforms, mediaFiles.map(f => URL.createObjectURL(f)));

    if (isScheduled && scheduledDate && scheduledTime) {
      const scheduledAt = new Date(`${scheduledDate}T${scheduledTime}`).getTime();
      schedulePost(postId, scheduledAt);
    } else {
      await publishPost(postId);
    }

    // Reset form
    setContent('');
    setSelectedPlatforms([]);
    setScheduledDate('');
    setScheduledTime('');
    setMediaFiles([]);
    setIsScheduled(false);
    
    if (onClose) onClose();
  };

  const generateAIContent = () => {
    // Mock AI content generation
    const suggestions = [
      "🚀 Exciting news! We're launching something amazing today. Stay tuned for updates! #innovation #launch",
      "💡 Pro tip: The best productivity hack is finding tools that work with your workflow, not against it. #productivity #tips",
      "🌟 Behind the scenes: Here's how we built our latest feature from concept to launch. #development #process",
      "🎯 What's your biggest challenge when it comes to staying organized? Let us know in the comments! #productivity #question",
      "📈 This week's metrics are looking great! Thank you to our amazing community for the continued support. #growth #community"
    ];
    
    const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
    setContent(randomSuggestion);
  };

  const characterLimit = getCharacterLimit();
  const remainingChars = characterLimit - content.length;
  const isOverLimit = remainingChars < 0;

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-900">Create Post</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={generateAIContent}
              className="flex items-center gap-2 px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors"
            >
              <Zap className="w-4 h-4" />
              AI Generate
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Platform Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Platforms
          </label>
          <div className="flex flex-wrap gap-2">
            {connectedPlatforms.map((platform) => {
              const Icon = platformIcons[platform.name];
              const isSelected = selectedPlatforms.includes(platform.id);
              
              return (
                <button
                  key={platform.id}
                  onClick={() => handlePlatformToggle(platform.id)}
                  className={`flex items-center gap-2 px-3 py-2 border rounded-lg transition-colors ${
                    isSelected
                      ? `${platformColors[platform.name]} bg-blue-50`
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <Icon className={`w-4 h-4 ${isSelected ? platformColors[platform.name].split(' ')[0] : 'text-gray-400'}`} />
                  <span className="text-sm">{platform.displayName}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Content Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Content
          </label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="What's happening?"
            className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 resize-none"
          />
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-4 text-sm text-gray-500">
              {getHashtags().length > 0 && (
                <div className="flex items-center gap-1">
                  <Hash className="w-4 h-4" />
                  <span>{getHashtags().length} hashtags</span>
                </div>
              )}
              {getMentions().length > 0 && (
                <div className="flex items-center gap-1">
                  <AtSign className="w-4 h-4" />
                  <span>{getMentions().length} mentions</span>
                </div>
              )}
            </div>
            <div className={`text-sm ${isOverLimit ? 'text-red-600' : 'text-gray-500'}`}>
              {remainingChars} characters remaining
            </div>
          </div>
        </div>

        {/* Media Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Media
          </label>
          <div className="flex items-center gap-2">
            <label className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <Image className="w-4 h-4 text-gray-400" />
              <span className="text-sm">Add Images</span>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleMediaUpload}
                className="hidden"
              />
            </label>
          </div>
          
          {mediaFiles.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {mediaFiles.map((file, index) => (
                <div key={index} className="relative">
                  <img
                    src={URL.createObjectURL(file)}
                    alt={`Upload ${index + 1}`}
                    className="w-16 h-16 object-cover rounded-lg"
                  />
                  <button
                    onClick={() => removeMediaFile(index)}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Scheduling */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <input
              type="checkbox"
              id="schedule"
              checked={isScheduled}
              onChange={(e) => setIsScheduled(e.target.checked)}
              className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
            />
            <label htmlFor="schedule" className="text-sm font-medium text-gray-700">
              Schedule for later
            </label>
          </div>
          
          {isScheduled && (
            <div className="flex gap-2">
              <input
                type="date"
                value={scheduledDate}
                onChange={(e) => setScheduledDate(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
              />
              <input
                type="time"
                value={scheduledTime}
                onChange={(e) => setScheduledTime(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
              />
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <button
            onClick={handlePublish}
            disabled={!content.trim() || selectedPlatforms.length === 0 || isOverLimit || isPublishing}
            className="flex items-center gap-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isScheduled ? (
              <>
                <Clock className="w-4 h-4" />
                Schedule Post
              </>
            ) : (
              <>
                <Send className="w-4 h-4" />
                {isPublishing ? 'Publishing...' : 'Publish Now'}
              </>
            )}
          </button>
          
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
