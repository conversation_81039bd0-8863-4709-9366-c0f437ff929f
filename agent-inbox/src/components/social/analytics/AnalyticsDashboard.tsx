"use client";

import React, { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Heart, 
  Share2, 
  MessageCircle,
  Eye,
  Calendar,
  Filter,
  Download
} from 'lucide-react';
import { useSocialStore } from '@/store/social/socialStore';

interface AnalyticsDashboardProps {
  className?: string;
}

export function AnalyticsDashboard({ className }: AnalyticsDashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month' | 'year'>('week');
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');

  const {
    connectedPlatforms,
    recentPosts,
    totalFollowers,
    totalReach,
    totalEngagement,
    loadAnalytics,
    refreshAnalytics,
  } = useSocialStore();

  // Mock analytics data
  const analyticsData = {
    followers: {
      current: totalFollowers,
      change: 245,
      changePercent: 2.1,
      trend: 'up' as const,
    },
    reach: {
      current: totalReach,
      change: 1200,
      changePercent: 15.2,
      trend: 'up' as const,
    },
    engagement: {
      current: totalEngagement,
      change: -12,
      changePercent: -7.1,
      trend: 'down' as const,
    },
    posts: {
      current: 12,
      change: 3,
      changePercent: 33.3,
      trend: 'up' as const,
    },
  };

  const topPosts = recentPosts
    .filter(post => post.analytics)
    .sort((a, b) => (b.analytics?.reach || 0) - (a.analytics?.reach || 0))
    .slice(0, 5);

  const platformPerformance = connectedPlatforms.map(platform => ({
    ...platform,
    engagement: Math.floor(Math.random() * 100) + 50,
    reach: Math.floor(Math.random() * 5000) + 1000,
    posts: Math.floor(Math.random() * 10) + 5,
  }));

  const handlePeriodChange = (period: 'day' | 'week' | 'month' | 'year') => {
    setSelectedPeriod(period);
    loadAnalytics(selectedPlatform === 'all' ? undefined : selectedPlatform, period);
  };

  const handlePlatformChange = (platformId: string) => {
    setSelectedPlatform(platformId);
    loadAnalytics(platformId === 'all' ? undefined : platformId, selectedPeriod);
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatChange = (change: number, percent: number, trend: 'up' | 'down') => {
    const TrendIcon = trend === 'up' ? TrendingUp : TrendingDown;
    const colorClass = trend === 'up' ? 'text-green-600' : 'text-red-600';
    
    return (
      <div className={`flex items-center gap-1 ${colorClass}`}>
        <TrendIcon className="w-4 h-4" />
        <span className="text-sm font-medium">
          {trend === 'up' ? '+' : ''}{change} ({percent > 0 ? '+' : ''}{percent.toFixed(1)}%)
        </span>
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-pink-600" />
            <h3 className="font-semibold text-gray-900">Analytics Dashboard</h3>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Period Filter */}
            <select
              value={selectedPeriod}
              onChange={(e) => handlePeriodChange(e.target.value as any)}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="day">Last 24 hours</option>
              <option value="week">Last 7 days</option>
              <option value="month">Last 30 days</option>
              <option value="year">Last year</option>
            </select>
            
            {/* Platform Filter */}
            <select
              value={selectedPlatform}
              onChange={(e) => handlePlatformChange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="all">All Platforms</option>
              {connectedPlatforms.map(platform => (
                <option key={platform.id} value={platform.id}>
                  {platform.displayName}
                </option>
              ))}
            </select>
            
            <button
              onClick={refreshAnalytics}
              className="flex items-center gap-2 px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Followers</span>
            </div>
            <div className="text-2xl font-bold text-blue-900 mb-1">
              {formatNumber(analyticsData.followers.current)}
            </div>
            {formatChange(
              analyticsData.followers.change,
              analyticsData.followers.changePercent,
              analyticsData.followers.trend
            )}
          </div>
          
          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Eye className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-green-900">Reach</span>
            </div>
            <div className="text-2xl font-bold text-green-900 mb-1">
              {formatNumber(analyticsData.reach.current)}
            </div>
            {formatChange(
              analyticsData.reach.change,
              analyticsData.reach.changePercent,
              analyticsData.reach.trend
            )}
          </div>
          
          <div className="p-4 bg-purple-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Heart className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-medium text-purple-900">Engagement</span>
            </div>
            <div className="text-2xl font-bold text-purple-900 mb-1">
              {formatNumber(analyticsData.engagement.current)}
            </div>
            {formatChange(
              analyticsData.engagement.change,
              analyticsData.engagement.changePercent,
              analyticsData.engagement.trend
            )}
          </div>
          
          <div className="p-4 bg-orange-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="w-5 h-5 text-orange-600" />
              <span className="text-sm font-medium text-orange-900">Posts</span>
            </div>
            <div className="text-2xl font-bold text-orange-900 mb-1">
              {analyticsData.posts.current}
            </div>
            {formatChange(
              analyticsData.posts.change,
              analyticsData.posts.changePercent,
              analyticsData.posts.trend
            )}
          </div>
        </div>

        {/* Platform Performance */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Platform Performance</h4>
          <div className="space-y-3">
            {platformPerformance.map((platform) => (
              <div key={platform.id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                <div className="font-medium text-sm w-20">{platform.displayName}</div>
                
                <div className="flex-1 grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="text-gray-500">Followers</div>
                    <div className="font-medium">{formatNumber(platform.followers)}</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Reach</div>
                    <div className="font-medium">{formatNumber(platform.reach)}</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Engagement</div>
                    <div className="font-medium">{platform.engagement}</div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-xs text-gray-500">Engagement Rate</div>
                  <div className="font-medium text-sm">
                    {((platform.engagement / platform.reach) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Performing Posts */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Top Performing Posts</h4>
          <div className="space-y-3">
            {topPosts.map((post, index) => (
              <div key={post.id} className="flex items-start gap-4 p-3 bg-gray-50 rounded-lg">
                <div className="w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center text-sm font-medium text-pink-700">
                  #{index + 1}
                </div>
                
                <div className="flex-1">
                  <div className="text-sm text-gray-900 mb-2 line-clamp-2">
                    {post.content}
                  </div>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Heart className="w-3 h-3" />
                      {post.analytics?.likes || 0}
                    </div>
                    <div className="flex items-center gap-1">
                      <Share2 className="w-3 h-3" />
                      {post.analytics?.shares || 0}
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageCircle className="w-3 h-3" />
                      {post.analytics?.comments || 0}
                    </div>
                    <div className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {formatNumber(post.analytics?.reach || 0)}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {formatNumber(post.analytics?.reach || 0)}
                  </div>
                  <div className="text-xs text-gray-500">reach</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
