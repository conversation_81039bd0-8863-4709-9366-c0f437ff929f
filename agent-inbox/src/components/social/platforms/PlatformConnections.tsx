"use client";

import React, { useState } from 'react';
import { 
  Twitter, 
  Facebook, 
  Instagram, 
  Linkedin, 
  Plus, 
  Settings, 
  RefreshCw,
  Users,
  TrendingUp,
  Calendar
} from 'lucide-react';
import { useSocialStore } from '@/store/social/socialStore';

interface PlatformConnectionsProps {
  className?: string;
}

const platformIcons = {
  twitter: Twitter,
  facebook: Facebook,
  instagram: Instagram,
  linkedin: Linkedin,
};

const platformColors = {
  twitter: 'text-blue-500',
  facebook: 'text-blue-600',
  instagram: 'text-pink-500',
  linkedin: 'text-blue-700',
};

export function PlatformConnections({ className }: PlatformConnectionsProps) {
  const [showConnectDialog, setShowConnectDialog] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);

  const {
    platforms,
    connectedPlatforms,
    totalFollowers,
    totalReach,
    totalEngagement,
    connectPlatform,
    disconnectPlatform,
    refreshPlatformData,
  } = useSocialStore();

  const handleConnectPlatform = async (platformName: string) => {
    setIsConnecting(true);
    setSelectedPlatform(platformName);
    
    try {
      // Mock OAuth flow - in real implementation, this would redirect to OAuth
      const mockCredentials = {
        accessToken: `mock_token_${Date.now()}`,
        refreshToken: `mock_refresh_${Date.now()}`,
        expiresAt: Date.now() + 3600000, // 1 hour
      };
      
      await connectPlatform(platformName, mockCredentials);
      setShowConnectDialog(false);
    } catch (error) {
      console.error('Failed to connect platform:', error);
    } finally {
      setIsConnecting(false);
      setSelectedPlatform(null);
    }
  };

  const handleDisconnectPlatform = (platformId: string) => {
    if (window.confirm('Are you sure you want to disconnect this platform?')) {
      disconnectPlatform(platformId);
    }
  };

  const handleRefreshPlatform = async (platformId: string) => {
    await refreshPlatformData(platformId);
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatLastSync = (timestamp: number): string => {
    if (timestamp === 0) return 'Never';
    
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-900">Connected Platforms</h3>
          <button
            onClick={() => setShowConnectDialog(true)}
            className="flex items-center gap-2 px-3 py-1 text-sm bg-pink-100 text-pink-700 rounded-lg hover:bg-pink-200 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Connect
          </button>
        </div>
      </div>

      <div className="p-4">
        {/* Platform List */}
        <div className="space-y-3 mb-6">
          {platforms.map((platform) => {
            const Icon = platformIcons[platform.name];
            const colorClass = platformColors[platform.name];
            
            return (
              <div
                key={platform.id}
                className={`p-3 border rounded-lg transition-colors ${
                  platform.connected 
                    ? 'hover:bg-pink-50 border-green-200 bg-green-50' 
                    : 'hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center gap-3">
                  <Icon className={`w-5 h-5 ${colorClass}`} />
                  <div className="flex-1">
                    <div className="font-medium text-sm">{platform.displayName}</div>
                    <div className="text-xs text-gray-500">
                      {platform.connected ? (
                        <>
                          {platform.username} • {formatNumber(platform.followers)} followers
                        </>
                      ) : (
                        'Not connected'
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {platform.connected && (
                      <>
                        <button
                          onClick={() => handleRefreshPlatform(platform.id)}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Refresh data"
                        >
                          <RefreshCw className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDisconnectPlatform(platform.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Disconnect"
                        >
                          <Settings className="w-4 h-4" />
                        </button>
                      </>
                    )}
                    
                    <div className={`w-2 h-2 rounded-full ${
                      platform.connected ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                  </div>
                </div>
                
                {platform.connected && (
                  <div className="mt-2 text-xs text-gray-500">
                    Last sync: {formatLastSync(platform.lastSync)}
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Quick Stats */}
        <div className="p-4 bg-pink-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-3">Today's Overview</h4>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center w-8 h-8 bg-pink-100 rounded-lg mx-auto mb-1">
                <Users className="w-4 h-4 text-pink-600" />
              </div>
              <div className="text-sm font-medium">{formatNumber(totalFollowers)}</div>
              <div className="text-xs text-gray-500">Followers</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center w-8 h-8 bg-pink-100 rounded-lg mx-auto mb-1">
                <TrendingUp className="w-4 h-4 text-pink-600" />
              </div>
              <div className="text-sm font-medium">{formatNumber(totalReach)}</div>
              <div className="text-xs text-gray-500">Reach</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center w-8 h-8 bg-pink-100 rounded-lg mx-auto mb-1">
                <Calendar className="w-4 h-4 text-pink-600" />
              </div>
              <div className="text-sm font-medium">{totalEngagement}</div>
              <div className="text-xs text-gray-500">Engagement</div>
            </div>
          </div>
        </div>
      </div>

      {/* Connect Platform Dialog */}
      {showConnectDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-full mx-4">
            <h3 className="font-semibold text-gray-900 mb-4">Connect Platform</h3>
            
            <div className="space-y-3 mb-6">
              {platforms.filter(p => !p.connected).map((platform) => {
                const Icon = platformIcons[platform.name];
                const colorClass = platformColors[platform.name];
                
                return (
                  <button
                    key={platform.id}
                    onClick={() => handleConnectPlatform(platform.name)}
                    disabled={isConnecting}
                    className="w-full flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                  >
                    <Icon className={`w-5 h-5 ${colorClass}`} />
                    <div className="flex-1 text-left">
                      <div className="font-medium text-sm">{platform.displayName}</div>
                      <div className="text-xs text-gray-500">
                        Connect your {platform.displayName} account
                      </div>
                    </div>
                    {isConnecting && selectedPlatform === platform.name && (
                      <RefreshCw className="w-4 h-4 animate-spin text-gray-400" />
                    )}
                  </button>
                );
              })}
            </div>
            
            <div className="flex gap-2 justify-end">
              <button
                onClick={() => setShowConnectDialog(false)}
                disabled={isConnecting}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
