"use client";

import React, { useState } from 'react';
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight, 
  Plus, 
  Clock, 
  Edit3, 
  Trash2,
  Twitter,
  Facebook,
  Instagram,
  Linkedin
} from 'lucide-react';
import { useSocialStore } from '@/store/social/socialStore';

interface ContentCalendarProps {
  className?: string;
}

const platformIcons = {
  twitter: Twitter,
  facebook: Facebook,
  instagram: Instagram,
  linkedin: Linkedin,
};

const platformColors = {
  twitter: 'bg-blue-100 text-blue-700',
  facebook: 'bg-blue-100 text-blue-800',
  instagram: 'bg-pink-100 text-pink-700',
  linkedin: 'bg-blue-100 text-blue-900',
};

export function ContentCalendar({ className }: ContentCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showPostDialog, setShowPostDialog] = useState(false);

  const {
    scheduledPosts,
    connectedPlatforms,
    getPlatformById,
    deletePost,
    updatePost,
  } = useSocialStore();

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const getPostsForDate = (date: Date) => {
    if (!date) return [];
    
    return scheduledPosts.filter(post => {
      if (!post.scheduledAt) return false;
      const postDate = new Date(post.scheduledAt);
      return (
        postDate.getDate() === date.getDate() &&
        postDate.getMonth() === date.getMonth() &&
        postDate.getFullYear() === date.getFullYear()
      );
    });
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
  };

  const handleDeletePost = (postId: string) => {
    if (window.confirm('Are you sure you want to delete this scheduled post?')) {
      deletePost(postId);
    }
  };

  const days = getDaysInMonth(currentDate);
  const monthYear = currentDate.toLocaleDateString('en-US', { 
    month: 'long', 
    year: 'numeric' 
  });

  const selectedDatePosts = selectedDate ? getPostsForDate(selectedDate) : [];

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-pink-600" />
            <h3 className="font-semibold text-gray-900">Content Calendar</h3>
          </div>
          
          <button
            onClick={() => setShowPostDialog(true)}
            className="flex items-center gap-2 px-3 py-1 text-sm bg-pink-100 text-pink-700 rounded-lg hover:bg-pink-200 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Schedule Post
          </button>
        </div>
      </div>

      <div className="p-4">
        {/* Calendar Header */}
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={() => navigateMonth('prev')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
          
          <h4 className="text-lg font-semibold text-gray-900">{monthYear}</h4>
          
          <button
            onClick={() => navigateMonth('next')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1 mb-4">
          {/* Day headers */}
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
          
          {/* Calendar days */}
          {days.map((date, index) => {
            if (!date) {
              return <div key={index} className="p-2 h-24" />;
            }
            
            const posts = getPostsForDate(date);
            const isToday = date.toDateString() === new Date().toDateString();
            const isSelected = selectedDate?.toDateString() === date.toDateString();
            
            return (
              <div
                key={index}
                onClick={() => handleDateClick(date)}
                className={`p-2 h-24 border rounded-lg cursor-pointer transition-colors ${
                  isSelected
                    ? 'border-pink-500 bg-pink-50'
                    : isToday
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className={`text-sm font-medium mb-1 ${
                  isToday ? 'text-blue-600' : 'text-gray-900'
                }`}>
                  {date.getDate()}
                </div>
                
                <div className="space-y-1">
                  {posts.slice(0, 2).map((post) => {
                    const platform = getPlatformById(post.platforms[0]);
                    if (!platform) return null;
                    
                    const Icon = platformIcons[platform.name];
                    const colorClass = platformColors[platform.name];
                    
                    return (
                      <div
                        key={post.id}
                        className={`text-xs px-1 py-0.5 rounded ${colorClass} truncate`}
                        title={post.content}
                      >
                        <Icon className="w-3 h-3 inline mr-1" />
                        {post.scheduledAt && formatTime(post.scheduledAt)}
                      </div>
                    );
                  })}
                  
                  {posts.length > 2 && (
                    <div className="text-xs text-gray-500">
                      +{posts.length - 2} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Selected Date Details */}
        {selectedDate && (
          <div className="border-t pt-4">
            <h5 className="font-medium text-gray-900 mb-3">
              {formatDate(selectedDate)}
            </h5>
            
            {selectedDatePosts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No posts scheduled for this date</p>
                <button
                  onClick={() => setShowPostDialog(true)}
                  className="mt-2 text-sm text-pink-600 hover:text-pink-700"
                >
                  Schedule a post
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                {selectedDatePosts.map((post) => {
                  const platform = getPlatformById(post.platforms[0]);
                  if (!platform) return null;
                  
                  const Icon = platformIcons[platform.name];
                  
                  return (
                    <div key={post.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                      <div className={`p-2 rounded-lg ${platformColors[platform.name]}`}>
                        <Icon className="w-4 h-4" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">{platform.displayName}</span>
                          {post.scheduledAt && (
                            <div className="flex items-center gap-1 text-xs text-gray-500">
                              <Clock className="w-3 h-3" />
                              {formatTime(post.scheduledAt)}
                            </div>
                          )}
                        </div>
                        <p className="text-sm text-gray-700 line-clamp-2">
                          {post.content}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => {/* TODO: Edit post */}}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Edit post"
                        >
                          <Edit3 className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeletePost(post.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete post"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
