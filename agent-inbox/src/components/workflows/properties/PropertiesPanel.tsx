"use client";

import React, { useState } from 'react';
import { X, Settings, Key, Database, Zap, Shield, Globe } from 'lucide-react';
import { useWorkflowStore } from '@/store/workflows/workflowStore';
import ApiKeyManager from '../settings/ApiKeyManager';

interface PropertiesPanelProps {
  className?: string;
}

export default function PropertiesPanel({ className }: PropertiesPanelProps) {
  const { showPropertiesPanel, togglePropertiesPanel, activeWorkflow, selectedNodes } = useWorkflowStore();
  const [activeTab, setActiveTab] = useState<'workflow' | 'node' | 'api-keys' | 'settings'>('workflow');

  if (!showPropertiesPanel) return null;

  const tabs = [
    { id: 'workflow', name: 'Workflow', icon: Settings, description: 'Workflow settings and configuration' },
    { id: 'node', name: 'Node', icon: Database, description: 'Selected node properties', disabled: selectedNodes.length === 0 },
    { id: 'api-keys', name: 'API Keys', icon: Key, description: 'Manage API keys for external services' },
    { id: 'settings', name: 'Settings', icon: Shield, description: 'Global workflow settings' },
  ];

  return (
    <div className={`bg-white border-l border-gray-200 flex flex-col ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Properties</h3>
          <button
            onClick={togglePropertiesPanel}
            className="p-1 text-gray-400 hover:text-gray-600 rounded"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-0">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => !tab.disabled && setActiveTab(tab.id as any)}
                disabled={tab.disabled}
                className={`flex-1 px-3 py-2 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-red-500 text-red-600 bg-red-50'
                    : tab.disabled
                    ? 'border-transparent text-gray-400 cursor-not-allowed'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-center gap-1">
                  <Icon className="w-4 h-4" />
                  <span className="hidden sm:inline">{tab.name}</span>
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'workflow' && (
          <div className="p-4 space-y-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Workflow Information</h4>
              {activeWorkflow ? (
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <input
                      type="text"
                      value={activeWorkflow.name}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea
                      value={activeWorkflow.description || ''}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      rows={3}
                      readOnly
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Nodes</label>
                      <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-sm">
                        {activeWorkflow.nodes.length}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Connections</label>
                      <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-sm">
                        {activeWorkflow.connections.length}
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <div className={`px-3 py-2 rounded-lg text-sm font-medium ${
                      activeWorkflow.status === 'published' ? 'bg-green-100 text-green-700' :
                      activeWorkflow.status === 'draft' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {activeWorkflow.status}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Settings className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-600">No workflow selected</p>
                  <p className="text-sm text-gray-500">Create or select a workflow to view its properties</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'node' && (
          <div className="p-4 space-y-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Node Properties</h4>
              {selectedNodes.length > 0 ? (
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Selected Nodes</label>
                    <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-sm">
                      {selectedNodes.length} node(s) selected
                    </div>
                  </div>
                  {selectedNodes.length === 1 && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Node Type</label>
                        <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-sm">
                          {selectedNodes[0].type}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Node ID</label>
                        <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-sm font-mono">
                          {selectedNodes[0].id}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">X Position</label>
                          <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-sm">
                            {selectedNodes[0].position.x}
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Y Position</label>
                          <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-sm">
                            {selectedNodes[0].position.y}
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Database className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-600">No nodes selected</p>
                  <p className="text-sm text-gray-500">Select a node on the canvas to view its properties</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'api-keys' && (
          <div className="p-4">
            <ApiKeyManager />
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="p-4 space-y-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Global Settings</h4>
              <div className="space-y-4">
                <div>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className="text-sm text-gray-700">Auto-save workflows</span>
                  </label>
                </div>
                <div>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className="text-sm text-gray-700">Show grid on canvas</span>
                  </label>
                </div>
                <div>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm text-gray-700">Enable debug mode</span>
                  </label>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Default execution timeout</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    <option value="30">30 seconds</option>
                    <option value="60">1 minute</option>
                    <option value="300">5 minutes</option>
                    <option value="600">10 minutes</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Canvas theme</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="auto">Auto</option>
                  </select>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-3">Security</h4>
              <div className="space-y-4">
                <div>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className="text-sm text-gray-700">Encrypt API keys</span>
                  </label>
                </div>
                <div>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm text-gray-700">Require authentication for execution</span>
                  </label>
                </div>
                <div>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className="text-sm text-gray-700">Log workflow executions</span>
                  </label>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-3">Performance</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Max concurrent executions</label>
                  <input
                    type="number"
                    defaultValue={5}
                    min={1}
                    max={20}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Memory limit per execution (MB)</label>
                  <input
                    type="number"
                    defaultValue={512}
                    min={128}
                    max={2048}
                    step={128}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
