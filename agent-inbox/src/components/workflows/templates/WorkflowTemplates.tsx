"use client";

import React, { useState } from 'react';
import { Search, Star, Download, Eye, Clock, Users, Zap, FileText, MessageSquare, Database, Code, Brain } from 'lucide-react';
import { useWorkflowStore } from '@/store/workflows/workflowStore';

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: 'ai' | 'automation' | 'data' | 'communication' | 'productivity';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string;
  rating: number;
  downloads: number;
  author: string;
  tags: string[];
  thumbnail?: string;
  nodes: any[];
  connections: any[];
}

const workflowTemplates: WorkflowTemplate[] = [
  {
    id: 'template-1',
    name: 'AI Content Generator',
    description: 'Generate high-quality content using AI with customizable prompts and output formatting',
    category: 'ai',
    difficulty: 'beginner',
    estimatedTime: '5 min',
    rating: 4.8,
    downloads: 1247,
    author: 'AI Team',
    tags: ['content', 'ai', 'writing', 'automation'],
    nodes: [
      {
        id: 'input-1',
        type: 'TextInput',
        position: { x: 100, y: 100 },
        data: { label: 'Content Topic', placeholder: 'Enter topic...' }
      },
      {
        id: 'ai-1',
        type: 'OpenAIChat',
        position: { x: 400, y: 100 },
        data: { 
          label: 'AI Content Generator',
          systemPrompt: 'You are a professional content writer. Create engaging, well-structured content on the given topic.'
        }
      },
      {
        id: 'output-1',
        type: 'TextOutput',
        position: { x: 700, y: 100 },
        data: { label: 'Generated Content' }
      }
    ],
    connections: [
      { id: 'conn-1', sourceNodeId: 'input-1', sourcePortId: 'text', targetNodeId: 'ai-1', targetPortId: 'prompt' },
      { id: 'conn-2', sourceNodeId: 'ai-1', sourcePortId: 'response', targetNodeId: 'output-1', targetPortId: 'text' }
    ]
  },
  {
    id: 'template-2',
    name: 'Data Processing Pipeline',
    description: 'Process and transform data through multiple stages with validation and error handling',
    category: 'data',
    difficulty: 'intermediate',
    estimatedTime: '10 min',
    rating: 4.6,
    downloads: 892,
    author: 'Data Team',
    tags: ['data', 'processing', 'validation', 'transformation'],
    nodes: [
      {
        id: 'input-1',
        type: 'TextInput',
        position: { x: 100, y: 100 },
        data: { label: 'Raw Data', placeholder: 'Enter data...' }
      },
      {
        id: 'process-1',
        type: 'DataProcessor',
        position: { x: 400, y: 100 },
        data: { label: 'Data Validator', operation: 'validate' }
      },
      {
        id: 'process-2',
        type: 'DataProcessor',
        position: { x: 700, y: 100 },
        data: { label: 'Data Transformer', operation: 'transform' }
      },
      {
        id: 'output-1',
        type: 'TextOutput',
        position: { x: 1000, y: 100 },
        data: { label: 'Processed Data' }
      }
    ],
    connections: [
      { id: 'conn-1', sourceNodeId: 'input-1', sourcePortId: 'text', targetNodeId: 'process-1', targetPortId: 'input' },
      { id: 'conn-2', sourceNodeId: 'process-1', sourcePortId: 'output', targetNodeId: 'process-2', targetPortId: 'input' },
      { id: 'conn-3', sourceNodeId: 'process-2', sourcePortId: 'output', targetNodeId: 'output-1', targetPortId: 'text' }
    ]
  },
  {
    id: 'template-3',
    name: 'Customer Support Bot',
    description: 'Automated customer support with AI-powered responses and escalation logic',
    category: 'communication',
    difficulty: 'advanced',
    estimatedTime: '15 min',
    rating: 4.9,
    downloads: 2156,
    author: 'Support Team',
    tags: ['support', 'ai', 'automation', 'customer-service'],
    nodes: [
      {
        id: 'input-1',
        type: 'TextInput',
        position: { x: 100, y: 100 },
        data: { label: 'Customer Query', placeholder: 'Enter customer question...' }
      },
      {
        id: 'ai-1',
        type: 'OpenAIChat',
        position: { x: 400, y: 100 },
        data: { 
          label: 'Support AI',
          systemPrompt: 'You are a helpful customer support agent. Provide clear, professional responses.'
        }
      },
      {
        id: 'condition-1',
        type: 'ConditionalRouter',
        position: { x: 700, y: 100 },
        data: { label: 'Escalation Check', condition: 'confidence < 0.8' }
      },
      {
        id: 'output-1',
        type: 'TextOutput',
        position: { x: 1000, y: 50 },
        data: { label: 'AI Response' }
      },
      {
        id: 'output-2',
        type: 'TextOutput',
        position: { x: 1000, y: 150 },
        data: { label: 'Escalate to Human' }
      }
    ],
    connections: [
      { id: 'conn-1', sourceNodeId: 'input-1', sourcePortId: 'text', targetNodeId: 'ai-1', targetPortId: 'prompt' },
      { id: 'conn-2', sourceNodeId: 'ai-1', sourcePortId: 'response', targetNodeId: 'condition-1', targetPortId: 'input' },
      { id: 'conn-3', sourceNodeId: 'condition-1', sourcePortId: 'true', targetNodeId: 'output-1', targetPortId: 'text' },
      { id: 'conn-4', sourceNodeId: 'condition-1', sourcePortId: 'false', targetNodeId: 'output-2', targetPortId: 'text' }
    ]
  }
];

const categoryIcons = {
  ai: Brain,
  automation: Zap,
  data: Database,
  communication: MessageSquare,
  productivity: FileText
};

const categoryColors = {
  ai: 'bg-purple-100 text-purple-700',
  automation: 'bg-blue-100 text-blue-700',
  data: 'bg-green-100 text-green-700',
  communication: 'bg-orange-100 text-orange-700',
  productivity: 'bg-gray-100 text-gray-700'
};

const difficultyColors = {
  beginner: 'bg-green-100 text-green-700',
  intermediate: 'bg-yellow-100 text-yellow-700',
  advanced: 'bg-red-100 text-red-700'
};

interface WorkflowTemplatesProps {
  onClose?: () => void;
}

export default function WorkflowTemplates({ onClose }: WorkflowTemplatesProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const { createWorkflowFromTemplate } = useWorkflowStore();

  const filteredTemplates = workflowTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'all' || template.difficulty === selectedDifficulty;
    
    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  const handleUseTemplate = async (template: WorkflowTemplate) => {
    try {
      await createWorkflowFromTemplate(template);
    } catch (error) {
      console.error('Failed to create workflow from template:', error);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Workflow Templates</h2>
            <p className="text-gray-600">Choose from pre-built templates to get started quickly</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
          >
            <option value="all">All Categories</option>
            <option value="ai">AI</option>
            <option value="automation">Automation</option>
            <option value="data">Data</option>
            <option value="communication">Communication</option>
            <option value="productivity">Productivity</option>
          </select>

          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
          >
            <option value="all">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => {
            const CategoryIcon = categoryIcons[template.category];
            
            return (
              <div key={template.id} className="bg-white rounded-lg border hover:shadow-lg transition-shadow">
                {/* Template Header */}
                <div className="p-6 border-b">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <CategoryIcon className="w-5 h-5 text-gray-600" />
                      <h3 className="font-semibold text-gray-900">{template.name}</h3>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600">{template.rating}</span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                  
                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span className={`px-2 py-1 rounded text-xs ${categoryColors[template.category]}`}>
                      {template.category}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs ${difficultyColors[template.difficulty]}`}>
                      {template.difficulty}
                    </span>
                    {template.tags.slice(0, 2).map((tag) => (
                      <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Template Stats */}
                <div className="px-6 py-4 border-b bg-gray-50">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{template.estimatedTime}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Download className="w-4 h-4" />
                      <span>{template.downloads.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      <span>{template.author}</span>
                    </div>
                  </div>
                </div>

                {/* Template Actions */}
                <div className="p-6">
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleUseTemplate(template)}
                      className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                    >
                      Use Template
                    </button>
                    <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-600">Try adjusting your search or filters</p>
          </div>
        )}
      </div>
    </div>
  );
}
