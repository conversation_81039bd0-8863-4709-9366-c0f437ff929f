"use client";

import React, { useState } from 'react';
import { Search, Star, Download, Eye, Clock, Users, Zap, FileText, MessageSquare, Database, Code, Brain } from 'lucide-react';
import { useWorkflowStore } from '@/store/workflows/workflowStore';

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: 'ai' | 'automation' | 'data' | 'communication' | 'productivity';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string;
  rating: number;
  downloads: number;
  author: string;
  tags: string[];
  thumbnail?: string;
  nodes: any[];
  connections: any[];
}

const workflowTemplates: WorkflowTemplate[] = [
  {
    id: 'basic-prompting',
    name: 'Basic Prompting',
    description: 'Simple AI prompting workflow for text generation and processing',
    category: 'ai',
    difficulty: 'beginner',
    estimatedTime: '2 min',
    rating: 4.9,
    downloads: 2156,
    author: 'Langflow Team',
    tags: ['prompting', 'ai', 'basic', 'text'],
    nodes: [
      {
        id: 'input-1',
        type: 'text-input',
        position: { x: 100, y: 100 },
        data: { label: 'User Input', placeholder: 'Enter your prompt...' }
      },
      {
        id: 'ai-1',
        type: 'openai-chat',
        position: { x: 400, y: 100 },
        data: {
          label: 'OpenAI Chat',
          systemPrompt: 'You are a helpful AI assistant. Respond clearly and concisely.'
        }
      },
      {
        id: 'output-1',
        type: 'text-output',
        position: { x: 700, y: 100 },
        data: { label: 'AI Response' }
      }
    ],
    connections: [
      { id: 'conn-1', sourceNodeId: 'input-1', sourcePortId: 'text', targetNodeId: 'ai-1', targetPortId: 'prompt' },
      { id: 'conn-2', sourceNodeId: 'ai-1', sourcePortId: 'response', targetNodeId: 'output-1', targetPortId: 'text' }
    ]
  },

  {
    id: 'simple-agent',
    name: 'Simple Agent',
    description: 'AI agent with tools for calculations and web content retrieval',
    category: 'ai',
    difficulty: 'intermediate',
    estimatedTime: '8 min',
    rating: 4.7,
    downloads: 1834,
    author: 'Langflow Team',
    tags: ['agent', 'tools', 'calculator', 'web'],
    nodes: [
      {
        id: 'input-1',
        type: 'text-input',
        position: { x: 100, y: 100 },
        data: { label: 'User Query', placeholder: 'Ask the agent anything...' }
      },
      {
        id: 'agent-1',
        type: 'openai-chat',
        position: { x: 400, y: 100 },
        data: {
          label: 'Agent Brain',
          systemPrompt: 'You are an AI agent with access to tools. Use the calculator for math and web scraper for online content.'
        }
      },
      {
        id: 'calc-1',
        type: 'random-generator',
        position: { x: 300, y: 250 },
        data: { label: 'Calculator Tool', type: 'number' }
      },
      {
        id: 'web-1',
        type: 'web-scraper',
        position: { x: 500, y: 250 },
        data: { label: 'Web Content Tool' }
      },
      {
        id: 'output-1',
        type: 'text-output',
        position: { x: 700, y: 100 },
        data: { label: 'Agent Response' }
      }
    ],
    connections: [
      { id: 'conn-1', source: 'input-1', target: 'agent-1', sourceHandle: 'output', targetHandle: 'prompt' },
      { id: 'conn-2', source: 'calc-1', target: 'agent-1', sourceHandle: 'value', targetHandle: 'tools' },
      { id: 'conn-3', source: 'web-1', target: 'agent-1', sourceHandle: 'content', targetHandle: 'tools' },
      { id: 'conn-4', source: 'agent-1', target: 'output-1', sourceHandle: 'response', targetHandle: 'input' }
    ]
  },

  {
    id: 'blog-writer',
    name: 'Blog Writer',
    description: 'AI-powered blog post generator with research and formatting capabilities',
    category: 'ai',
    difficulty: 'intermediate',
    estimatedTime: '10 min',
    rating: 4.8,
    downloads: 1456,
    author: 'Langflow Team',
    tags: ['blog', 'writing', 'content', 'research'],
    nodes: [
      {
        id: 'input-1',
        type: 'text-input',
        position: { x: 100, y: 100 },
        data: { label: 'Blog Topic', placeholder: 'Enter blog topic...' }
      },
      {
        id: 'research-1',
        type: 'web-scraper',
        position: { x: 300, y: 200 },
        data: { label: 'Research Tool' }
      },
      {
        id: 'writer-1',
        type: 'openai-chat',
        position: { x: 500, y: 100 },
        data: {
          label: 'Blog Writer',
          systemPrompt: 'You are a professional blog writer. Create engaging, well-structured blog posts with proper formatting.'
        }
      },
      {
        id: 'formatter-1',
        type: 'combine-text',
        position: { x: 700, y: 100 },
        data: { label: 'Content Formatter' }
      },
      {
        id: 'output-1',
        type: 'text-output',
        position: { x: 900, y: 100 },
        data: { label: 'Blog Post' }
      }
    ],
    connections: [
      { id: 'conn-1', source: 'input-1', target: 'research-1', sourceHandle: 'output', targetHandle: 'url' },
      { id: 'conn-2', source: 'input-1', target: 'writer-1', sourceHandle: 'output', targetHandle: 'prompt' },
      { id: 'conn-3', source: 'research-1', target: 'writer-1', sourceHandle: 'content', targetHandle: 'system' },
      { id: 'conn-4', source: 'writer-1', target: 'formatter-1', sourceHandle: 'response', targetHandle: 'text1' },
      { id: 'conn-5', source: 'formatter-1', target: 'output-1', sourceHandle: 'combined', targetHandle: 'input' }
    ]
  },

  {
    id: 'document-qa',
    name: 'Document QA',
    description: 'Question-answering system for documents using RAG (Retrieval Augmented Generation)',
    category: 'ai',
    difficulty: 'intermediate',
    estimatedTime: '12 min',
    rating: 4.6,
    downloads: 1289,
    author: 'Langflow Team',
    tags: ['qa', 'documents', 'rag', 'retrieval'],
    nodes: [
      {
        id: 'doc-1',
        type: 'file-loader',
        position: { x: 100, y: 100 },
        data: { label: 'Document Loader' }
      },
      {
        id: 'split-1',
        type: 'text-splitter',
        position: { x: 300, y: 100 },
        data: { label: 'Text Splitter', chunkSize: 1000 }
      },
      {
        id: 'embed-1',
        type: 'openai-embeddings',
        position: { x: 500, y: 100 },
        data: { label: 'Embeddings' }
      },
      {
        id: 'store-1',
        type: 'chroma-store',
        position: { x: 700, y: 100 },
        data: { label: 'Vector Store' }
      },
      {
        id: 'question-1',
        type: 'text-input',
        position: { x: 100, y: 300 },
        data: { label: 'Question', placeholder: 'Ask about the document...' }
      },
      {
        id: 'qa-1',
        type: 'openai-chat',
        position: { x: 500, y: 300 },
        data: {
          label: 'QA System',
          systemPrompt: 'Answer questions based on the provided document context. Be accurate and cite sources.'
        }
      },
      {
        id: 'output-1',
        type: 'text-output',
        position: { x: 700, y: 300 },
        data: { label: 'Answer' }
      }
    ],
    connections: [
      { id: 'conn-1', source: 'doc-1', target: 'split-1', sourceHandle: 'content', targetHandle: 'text' },
      { id: 'conn-2', source: 'split-1', target: 'embed-1', sourceHandle: 'chunks', targetHandle: 'text' },
      { id: 'conn-3', source: 'embed-1', target: 'store-1', sourceHandle: 'embeddings', targetHandle: 'vectors' },
      { id: 'conn-4', source: 'question-1', target: 'store-1', sourceHandle: 'output', targetHandle: 'query' },
      { id: 'conn-5', source: 'store-1', target: 'qa-1', sourceHandle: 'results', targetHandle: 'system' },
      { id: 'conn-6', source: 'question-1', target: 'qa-1', sourceHandle: 'output', targetHandle: 'prompt' },
      { id: 'conn-7', source: 'qa-1', target: 'output-1', sourceHandle: 'response', targetHandle: 'input' }
    ]
  },

  {
    id: 'memory-chatbot',
    name: 'Memory Chatbot',
    description: 'Conversational AI with persistent memory for context-aware interactions',
    category: 'ai',
    difficulty: 'intermediate',
    estimatedTime: '8 min',
    rating: 4.7,
    downloads: 1678,
    author: 'Langflow Team',
    tags: ['chatbot', 'memory', 'conversation', 'context'],
    nodes: [
      {
        id: 'input-1',
        type: 'text-input',
        position: { x: 100, y: 100 },
        data: { label: 'User Message', placeholder: 'Type your message...' }
      },
      {
        id: 'memory-1',
        type: 'memory-store',
        position: { x: 300, y: 200 },
        data: { label: 'Conversation Memory', operation: 'get' }
      },
      {
        id: 'chat-1',
        type: 'openai-chat',
        position: { x: 500, y: 100 },
        data: {
          label: 'Chatbot',
          systemPrompt: 'You are a helpful assistant with memory of our conversation. Use context from previous messages.'
        }
      },
      {
        id: 'save-memory-1',
        type: 'memory-store',
        position: { x: 700, y: 200 },
        data: { label: 'Save Memory', operation: 'set' }
      },
      {
        id: 'output-1',
        type: 'text-output',
        position: { x: 700, y: 100 },
        data: { label: 'Bot Response' }
      }
    ],
    connections: [
      { id: 'conn-1', source: 'input-1', target: 'memory-1', sourceHandle: 'output', targetHandle: 'key' },
      { id: 'conn-2', source: 'memory-1', target: 'chat-1', sourceHandle: 'stored', targetHandle: 'system' },
      { id: 'conn-3', source: 'input-1', target: 'chat-1', sourceHandle: 'output', targetHandle: 'prompt' },
      { id: 'conn-4', source: 'chat-1', target: 'output-1', sourceHandle: 'response', targetHandle: 'input' },
      { id: 'conn-5', source: 'chat-1', target: 'save-memory-1', sourceHandle: 'response', targetHandle: 'value' }
    ]
  },

  {
    id: 'vector-store-rag',
    name: 'Vector Store RAG',
    description: 'Advanced RAG system with vector database for semantic search and retrieval',
    category: 'ai',
    difficulty: 'advanced',
    estimatedTime: '15 min',
    rating: 4.8,
    downloads: 987,
    author: 'Langflow Team',
    tags: ['rag', 'vector', 'semantic', 'search'],
    nodes: [
      {
        id: 'docs-1',
        type: 'file-loader',
        position: { x: 100, y: 100 },
        data: { label: 'Document Collection' }
      },
      {
        id: 'split-1',
        type: 'text-splitter',
        position: { x: 300, y: 100 },
        data: { label: 'Chunk Splitter', chunkSize: 500, chunkOverlap: 50 }
      },
      {
        id: 'embed-1',
        type: 'openai-embeddings',
        position: { x: 500, y: 100 },
        data: { label: 'Generate Embeddings' }
      },
      {
        id: 'pinecone-1',
        type: 'pinecone-store',
        position: { x: 700, y: 100 },
        data: { label: 'Pinecone Vector DB' }
      },
      {
        id: 'query-1',
        type: 'text-input',
        position: { x: 100, y: 300 },
        data: { label: 'Search Query', placeholder: 'What would you like to know?' }
      },
      {
        id: 'search-1',
        type: 'pinecone-store',
        position: { x: 300, y: 300 },
        data: { label: 'Semantic Search' }
      },
      {
        id: 'generate-1',
        type: 'openai-chat',
        position: { x: 500, y: 300 },
        data: {
          label: 'Answer Generator',
          systemPrompt: 'Generate comprehensive answers based on the retrieved context. Cite sources when possible.'
        }
      },
      {
        id: 'output-1',
        type: 'text-output',
        position: { x: 700, y: 300 },
        data: { label: 'Generated Answer' }
      }
    ],
    connections: [
      { id: 'conn-1', source: 'docs-1', target: 'split-1', sourceHandle: 'content', targetHandle: 'text' },
      { id: 'conn-2', source: 'split-1', target: 'embed-1', sourceHandle: 'chunks', targetHandle: 'text' },
      { id: 'conn-3', source: 'embed-1', target: 'pinecone-1', sourceHandle: 'embeddings', targetHandle: 'vectors' },
      { id: 'conn-4', source: 'query-1', target: 'search-1', sourceHandle: 'output', targetHandle: 'query' },
      { id: 'conn-5', source: 'search-1', target: 'generate-1', sourceHandle: 'results', targetHandle: 'system' },
      { id: 'conn-6', source: 'query-1', target: 'generate-1', sourceHandle: 'output', targetHandle: 'prompt' },
      { id: 'conn-7', source: 'generate-1', target: 'output-1', sourceHandle: 'response', targetHandle: 'input' }
    ]
  },

  {
    id: 'financial-report-parser',
    name: 'Financial Report Parser',
    description: 'Extract and analyze financial data from reports and documents',
    category: 'data',
    difficulty: 'advanced',
    estimatedTime: '18 min',
    rating: 4.5,
    downloads: 743,
    author: 'Langflow Team',
    tags: ['finance', 'parsing', 'analysis', 'reports'],
    nodes: [
      {
        id: 'upload-1',
        type: 'file-loader',
        position: { x: 100, y: 100 },
        data: { label: 'Financial Report Upload' }
      },
      {
        id: 'extract-1',
        type: 'openai-chat',
        position: { x: 300, y: 100 },
        data: {
          label: 'Data Extractor',
          systemPrompt: 'Extract financial data from the document. Focus on revenue, expenses, profit margins, and key metrics.'
        }
      },
      {
        id: 'parse-1',
        type: 'json-parser',
        position: { x: 500, y: 100 },
        data: { label: 'Structure Parser' }
      },
      {
        id: 'analyze-1',
        type: 'openai-chat',
        position: { x: 700, y: 100 },
        data: {
          label: 'Financial Analyzer',
          systemPrompt: 'Analyze the financial data and provide insights on trends, ratios, and performance indicators.'
        }
      },
      {
        id: 'output-1',
        type: 'text-output',
        position: { x: 900, y: 100 },
        data: { label: 'Analysis Report' }
      }
    ],
    connections: [
      { id: 'conn-1', source: 'upload-1', target: 'extract-1', sourceHandle: 'content', targetHandle: 'prompt' },
      { id: 'conn-2', source: 'extract-1', target: 'parse-1', sourceHandle: 'response', targetHandle: 'json' },
      { id: 'conn-3', source: 'parse-1', target: 'analyze-1', sourceHandle: 'data', targetHandle: 'prompt' },
      { id: 'conn-4', source: 'analyze-1', target: 'output-1', sourceHandle: 'response', targetHandle: 'input' }
    ]
  },

  {
    id: 'sequential-tasks-agent',
    name: 'Sequential Tasks Agent',
    description: 'AI agent that executes multiple tasks in sequence with context passing',
    category: 'ai',
    difficulty: 'advanced',
    estimatedTime: '20 min',
    rating: 4.6,
    downloads: 892,
    author: 'Langflow Team',
    tags: ['agent', 'sequential', 'tasks', 'automation'],
    nodes: [
      {
        id: 'input-1',
        type: 'text-input',
        position: { x: 100, y: 100 },
        data: { label: 'Task List', placeholder: 'Enter tasks to complete...' }
      },
      {
        id: 'planner-1',
        type: 'openai-chat',
        position: { x: 300, y: 100 },
        data: {
          label: 'Task Planner',
          systemPrompt: 'Break down the task list into sequential steps with clear dependencies.'
        }
      },
      {
        id: 'executor-1',
        type: 'openai-chat',
        position: { x: 500, y: 100 },
        data: {
          label: 'Task Executor',
          systemPrompt: 'Execute each task step by step, using context from previous steps.'
        }
      },
      {
        id: 'memory-1',
        type: 'memory-store',
        position: { x: 400, y: 250 },
        data: { label: 'Context Memory', operation: 'set' }
      },
      {
        id: 'validator-1',
        type: 'openai-chat',
        position: { x: 700, y: 100 },
        data: {
          label: 'Result Validator',
          systemPrompt: 'Validate task completion and check if all requirements are met.'
        }
      },
      {
        id: 'output-1',
        type: 'text-output',
        position: { x: 900, y: 100 },
        data: { label: 'Completed Tasks' }
      }
    ],
    connections: [
      { id: 'conn-1', source: 'input-1', target: 'planner-1', sourceHandle: 'output', targetHandle: 'prompt' },
      { id: 'conn-2', source: 'planner-1', target: 'executor-1', sourceHandle: 'response', targetHandle: 'prompt' },
      { id: 'conn-3', source: 'executor-1', target: 'memory-1', sourceHandle: 'response', targetHandle: 'value' },
      { id: 'conn-4', source: 'memory-1', target: 'validator-1', sourceHandle: 'stored', targetHandle: 'system' },
      { id: 'conn-5', source: 'executor-1', target: 'validator-1', sourceHandle: 'response', targetHandle: 'prompt' },
      { id: 'conn-6', source: 'validator-1', target: 'output-1', sourceHandle: 'response', targetHandle: 'input' }
    ]
  },

  {
    id: 'travel-planning-agent',
    name: 'Travel Planning Agent',
    description: 'Comprehensive travel planning with research, booking assistance, and itinerary creation',
    category: 'productivity',
    difficulty: 'advanced',
    estimatedTime: '25 min',
    rating: 4.7,
    downloads: 1234,
    author: 'Langflow Team',
    tags: ['travel', 'planning', 'research', 'itinerary'],
    nodes: [
      {
        id: 'input-1',
        type: 'text-input',
        position: { x: 100, y: 100 },
        data: { label: 'Travel Requirements', placeholder: 'Destination, dates, preferences...' }
      },
      {
        id: 'research-1',
        type: 'web-scraper',
        position: { x: 300, y: 100 },
        data: { label: 'Destination Research' }
      },
      {
        id: 'planner-1',
        type: 'openai-chat',
        position: { x: 500, y: 100 },
        data: {
          label: 'Travel Planner',
          systemPrompt: 'Create detailed travel itineraries based on research and user preferences.'
        }
      },
      {
        id: 'budget-1',
        type: 'openai-chat',
        position: { x: 300, y: 250 },
        data: {
          label: 'Budget Calculator',
          systemPrompt: 'Calculate travel costs and provide budget breakdowns.'
        }
      },
      {
        id: 'formatter-1',
        type: 'combine-text',
        position: { x: 700, y: 100 },
        data: { label: 'Itinerary Formatter' }
      },
      {
        id: 'output-1',
        type: 'text-output',
        position: { x: 900, y: 100 },
        data: { label: 'Travel Plan' }
      }
    ],
    connections: [
      { id: 'conn-1', source: 'input-1', target: 'research-1', sourceHandle: 'output', targetHandle: 'url' },
      { id: 'conn-2', source: 'research-1', target: 'planner-1', sourceHandle: 'content', targetHandle: 'system' },
      { id: 'conn-3', source: 'input-1', target: 'planner-1', sourceHandle: 'output', targetHandle: 'prompt' },
      { id: 'conn-4', source: 'input-1', target: 'budget-1', sourceHandle: 'output', targetHandle: 'prompt' },
      { id: 'conn-5', source: 'planner-1', target: 'formatter-1', sourceHandle: 'response', targetHandle: 'text1' },
      { id: 'conn-6', source: 'budget-1', target: 'formatter-1', sourceHandle: 'response', targetHandle: 'text2' },
      { id: 'conn-7', source: 'formatter-1', target: 'output-1', sourceHandle: 'combined', targetHandle: 'input' }
    ]
  }
];

const categoryIcons = {
  ai: Brain,
  automation: Zap,
  data: Database,
  communication: MessageSquare,
  productivity: FileText
};

const categoryColors = {
  ai: 'bg-purple-100 text-purple-700',
  automation: 'bg-blue-100 text-blue-700',
  data: 'bg-green-100 text-green-700',
  communication: 'bg-orange-100 text-orange-700',
  productivity: 'bg-gray-100 text-gray-700'
};

const difficultyColors = {
  beginner: 'bg-green-100 text-green-700',
  intermediate: 'bg-yellow-100 text-yellow-700',
  advanced: 'bg-red-100 text-red-700'
};

interface WorkflowTemplatesProps {
  onClose?: () => void;
}

export default function WorkflowTemplates({ onClose }: WorkflowTemplatesProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const { createWorkflowFromTemplate } = useWorkflowStore();

  const filteredTemplates = workflowTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'all' || template.difficulty === selectedDifficulty;
    
    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  const handleUseTemplate = async (template: WorkflowTemplate) => {
    try {
      await createWorkflowFromTemplate(template);
      onClose?.(); // Close the modal after successful template creation
    } catch (error) {
      console.error('Failed to create workflow from template:', error);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Workflow Templates</h2>
            <p className="text-gray-600">Choose from pre-built templates to get started quickly</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
          >
            <option value="all">All Categories</option>
            <option value="ai">AI</option>
            <option value="automation">Automation</option>
            <option value="data">Data</option>
            <option value="communication">Communication</option>
            <option value="productivity">Productivity</option>
          </select>

          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
          >
            <option value="all">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => {
            const CategoryIcon = categoryIcons[template.category];
            
            return (
              <div key={template.id} className="bg-white rounded-lg border hover:shadow-lg transition-shadow">
                {/* Template Header */}
                <div className="p-6 border-b">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <CategoryIcon className="w-5 h-5 text-gray-600" />
                      <h3 className="font-semibold text-gray-900">{template.name}</h3>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600">{template.rating}</span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                  
                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span className={`px-2 py-1 rounded text-xs ${categoryColors[template.category]}`}>
                      {template.category}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs ${difficultyColors[template.difficulty]}`}>
                      {template.difficulty}
                    </span>
                    {template.tags.slice(0, 2).map((tag) => (
                      <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Template Stats */}
                <div className="px-6 py-4 border-b bg-gray-50">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{template.estimatedTime}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Download className="w-4 h-4" />
                      <span>{template.downloads.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      <span>{template.author}</span>
                    </div>
                  </div>
                </div>

                {/* Template Actions */}
                <div className="p-6">
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleUseTemplate(template)}
                      className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                    >
                      Use Template
                    </button>
                    <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-600">Try adjusting your search or filters</p>
          </div>
        )}
      </div>
    </div>
  );
}
