"use client";

import React, { useRef, useState, useCallback, useEffect } from 'react';
import { useDroppable } from '@dnd-kit/core';
import { 
  Play, 
  Save, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Grid3X3,
  Move,
  MousePointer
} from 'lucide-react';
import { useWorkflowStore } from '@/store/workflows/workflowStore';
import { WorkflowNode } from './WorkflowNode';
import { NodeConnections } from './NodeConnections';

interface WorkflowCanvasProps {
  className?: string;
}

export function WorkflowCanvas({ className }: WorkflowCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, y: 0 });
  const [showGrid, setShowGrid] = useState(true);

  const {
    activeWorkflow,
    canvasPosition,
    canvasZoom,
    selectedNodes,
    isExecuting,
    addNode,
    setCanvasPosition,
    setCanvasZoom,
    executeWorkflow,
    updateWorkflow,
    connectionCreation,
    updateConnectionCreation,
    cancelConnectionCreation,
  } = useWorkflowStore();

  const { setNodeRef, isOver } = useDroppable({
    id: 'workflow-canvas',
  });

  // Handle keyboard events for connection creation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && connectionCreation?.isCreating) {
        cancelConnectionCreation();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [connectionCreation, cancelConnectionCreation]);

  const handleCanvasMouseDown = useCallback((e: React.MouseEvent) => {
    if (connectionCreation?.isCreating) {
      // Cancel connection creation on canvas click
      cancelConnectionCreation();
      return;
    }

    if (e.button === 1 || (e.button === 0 && e.altKey)) { // Middle mouse or Alt+Left
      e.preventDefault();
      setIsPanning(true);
      setPanStart({ x: e.clientX - canvasPosition.x, y: e.clientY - canvasPosition.y });
    }
  }, [canvasPosition, connectionCreation, cancelConnectionCreation]);

  const handleCanvasMouseMove = useCallback((e: React.MouseEvent) => {
    if (connectionCreation?.isCreating) {
      // Update connection creation mouse position
      const rect = canvasRef.current?.getBoundingClientRect();
      if (rect) {
        const mousePosition = {
          x: (e.clientX - rect.left - canvasPosition.x) / canvasZoom,
          y: (e.clientY - rect.top - canvasPosition.y) / canvasZoom,
        };
        updateConnectionCreation(mousePosition);
      }
    } else if (isPanning) {
      const newPosition = {
        x: e.clientX - panStart.x,
        y: e.clientY - panStart.y,
      };
      setCanvasPosition(newPosition);
    }
  }, [isPanning, panStart, setCanvasPosition, connectionCreation, updateConnectionCreation, canvasPosition, canvasZoom]);

  const handleCanvasMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      const newZoom = Math.max(0.1, Math.min(3, canvasZoom + delta));
      setCanvasZoom(newZoom);
    }
  }, [canvasZoom, setCanvasZoom]);

  const handleZoomIn = () => {
    setCanvasZoom(Math.min(3, canvasZoom + 0.2));
  };

  const handleZoomOut = () => {
    setCanvasZoom(Math.max(0.1, canvasZoom - 0.2));
  };

  const handleResetView = () => {
    setCanvasPosition({ x: 0, y: 0 });
    setCanvasZoom(1);
  };

  const handleSaveWorkflow = () => {
    if (activeWorkflow) {
      updateWorkflow(activeWorkflow.id, { updatedAt: Date.now() });
      console.log('Workflow saved:', activeWorkflow.name);
    }
  };

  const handleExecuteWorkflow = () => {
    if (activeWorkflow && !isExecuting) {
      executeWorkflow(activeWorkflow.id);
    }
  };

  const getCanvasStyle = () => ({
    transform: `translate(${canvasPosition.x}px, ${canvasPosition.y}px) scale(${canvasZoom})`,
    transformOrigin: '0 0',
  });

  const getGridStyle = () => {
    const gridSize = 20 * canvasZoom;
    return {
      backgroundImage: `
        linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px)
      `,
      backgroundSize: `${gridSize}px ${gridSize}px`,
      backgroundPosition: `${canvasPosition.x % gridSize}px ${canvasPosition.y % gridSize}px`,
    };
  };

  return (
    <div className={`flex-1 flex flex-col bg-gray-100 ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-3 bg-white border-b">
        <button
          onClick={handleExecuteWorkflow}
          disabled={!activeWorkflow || isExecuting}
          className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <Play className="w-4 h-4" />
          {isExecuting ? 'Running...' : 'Run'}
        </button>
        
        <button
          onClick={handleSaveWorkflow}
          disabled={!activeWorkflow}
          className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <Save className="w-4 h-4" />
          Save
        </button>

        <div className="w-px h-6 bg-gray-300 mx-2" />

        <button
          onClick={handleZoomIn}
          className="p-2 rounded-lg border hover:bg-gray-50 transition-colors"
          title="Zoom In"
        >
          <ZoomIn className="w-4 h-4" />
        </button>
        
        <button
          onClick={handleZoomOut}
          className="p-2 rounded-lg border hover:bg-gray-50 transition-colors"
          title="Zoom Out"
        >
          <ZoomOut className="w-4 h-4" />
        </button>
        
        <button
          onClick={handleResetView}
          className="p-2 rounded-lg border hover:bg-gray-50 transition-colors"
          title="Reset View"
        >
          <RotateCcw className="w-4 h-4" />
        </button>

        <button
          onClick={() => setShowGrid(!showGrid)}
          className={`p-2 rounded-lg border transition-colors ${
            showGrid ? 'bg-indigo-50 text-indigo-600' : 'hover:bg-gray-50'
          }`}
          title="Toggle Grid"
        >
          <Grid3X3 className="w-4 h-4" />
        </button>

        <div className="flex-1" />

        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div>Zoom: {Math.round(canvasZoom * 100)}%</div>
          <div>
            Position: {Math.round(canvasPosition.x)}, {Math.round(canvasPosition.y)}
          </div>
          {activeWorkflow && (
            <div>
              Nodes: {activeWorkflow.nodes.length} | 
              Connections: {activeWorkflow.connections.length}
            </div>
          )}
        </div>
      </div>

      {/* Canvas */}
      <div
        ref={(node) => {
          setNodeRef(node);
          canvasRef.current = node;
        }}
        className={`flex-1 relative overflow-hidden cursor-${isPanning ? 'grabbing' : 'grab'} ${
          isOver ? 'bg-indigo-50' : ''
        }`}
        onMouseDown={handleCanvasMouseDown}
        onMouseMove={handleCanvasMouseMove}
        onMouseUp={handleCanvasMouseUp}
        onMouseLeave={handleCanvasMouseUp}
        onWheel={handleWheel}
      >
        {/* Grid Background */}
        {showGrid && (
          <div
            className="absolute inset-0 opacity-30"
            style={getGridStyle()}
          />
        )}

        {/* Canvas Content */}
        <div
          className="absolute inset-0"
          style={getCanvasStyle()}
        >
          {activeWorkflow ? (
            <>
              {/* Connections */}
              <NodeConnections
                nodes={activeWorkflow.nodes}
                connections={activeWorkflow.connections}
                zoom={canvasZoom}
              />

              {/* Nodes */}
              {activeWorkflow.nodes.map((node) => (
                <WorkflowNode
                  key={node.id}
                  node={node}
                  isSelected={selectedNodes.includes(node.id)}
                  zoom={canvasZoom}
                />
              ))}
            </>
          ) : (
            <div className="flex items-center justify-center w-full h-full">
              <div className="text-center">
                <div className="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <MousePointer className="w-10 h-10 text-indigo-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  Visual Workflow Builder
                </h3>
                <p className="text-gray-600 mb-6 max-w-md">
                  🚀 Drag-Drop Integration Complete<br/>
                  Drag components from the library to create powerful workflows.
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <div>• Drag components from the left panel</div>
                  <div>• Connect nodes to create data flow</div>
                  <div>• Click Run to execute your workflow</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Drop Indicator */}
        {isOver && (
          <div className="absolute inset-0 border-2 border-dashed border-indigo-400 bg-indigo-50 bg-opacity-50 flex items-center justify-center">
            <div className="text-indigo-600 font-medium">
              Drop component here to add to workflow
            </div>
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 bg-white border-t text-sm text-gray-600">
        <div className="flex items-center gap-4">
          {activeWorkflow ? (
            <>
              <span>Workflow: {activeWorkflow.name}</span>
              <span className={`px-2 py-1 rounded-full text-xs ${
                activeWorkflow.status === 'active' ? 'bg-green-100 text-green-700' :
                activeWorkflow.status === 'draft' ? 'bg-gray-100 text-gray-700' :
                activeWorkflow.status === 'error' ? 'bg-red-100 text-red-700' :
                'bg-blue-100 text-blue-700'
              }`}>
                {activeWorkflow.status}
              </span>
            </>
          ) : (
            <span>No workflow selected</span>
          )}
        </div>
        
        <div className="flex items-center gap-4">
          <div>Selected: {selectedNodes.length} nodes</div>
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${
              isExecuting ? 'bg-yellow-500' : 'bg-green-500'
            }`} />
            <span>{isExecuting ? 'Executing' : 'Ready'}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
