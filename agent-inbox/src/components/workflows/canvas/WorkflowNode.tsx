"use client";

import React, { useState, useRef } from 'react';
import { useDraggable } from '@dnd-kit/core';
import { 
  Type,
  Bot,
  FileText,
  Filter,
  Database,
  Code,
  Workflow,
  Grid,
  Settings,
  Trash2,
  Play,
  Square,
  CheckCircle,
  XCircle,
  Loader
} from 'lucide-react';
import { useWorkflowStore, WorkflowNode as WorkflowNodeType } from '@/store/workflows/workflowStore';

interface WorkflowNodeProps {
  node: WorkflowNodeType;
  isSelected: boolean;
  zoom: number;
}

const nodeIcons: Record<string, any> = {
  'text-input': Type,
  'openai-chat': Bot,
  'text-output': FileText,
  'data-filter': Filter,
  'database-query': Database,
  'code-executor': Code,
  'workflow-trigger': Workflow,
};

const categoryColors = {
  input: 'border-blue-300 bg-blue-50',
  output: 'border-green-300 bg-green-50',
  processing: 'border-orange-300 bg-orange-50',
  ai: 'border-purple-300 bg-purple-50',
  integration: 'border-indigo-300 bg-indigo-50',
};

const statusColors = {
  idle: 'text-gray-500',
  running: 'text-yellow-500',
  completed: 'text-green-500',
  error: 'text-red-500',
};

const statusIcons = {
  idle: Square,
  running: Loader,
  completed: CheckCircle,
  error: XCircle,
};

export function WorkflowNode({ node, isSelected, zoom }: WorkflowNodeProps) {
  const [showConfig, setShowConfig] = useState(false);
  const nodeRef = useRef<HTMLDivElement>(null);

  const {
    updateNode,
    deleteNode,
    setSelectedNodes,
    selectedNodes,
    addConnection,
    getNodeConnections,
    connectionCreation,
    startConnectionCreation,
    completeConnectionCreation,
    cancelConnectionCreation,
  } = useWorkflowStore();

  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `node-${node.id}`,
    data: { node },
  });

  const Icon = nodeIcons[node.name.toLowerCase().replace(/\s+/g, '-')] || Grid;
  const StatusIcon = statusIcons[node.status];
  const colorClass = categoryColors[node.category];
  const statusColor = statusColors[node.status];

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  const handleNodeClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (e.ctrlKey || e.metaKey) {
      // Multi-select
      const newSelection = selectedNodes.includes(node.id)
        ? selectedNodes.filter(id => id !== node.id)
        : [...selectedNodes, node.id];
      setSelectedNodes(newSelection);
    } else {
      // Single select
      setSelectedNodes([node.id]);
    }
  };

  const handleNodeDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowConfig(true);
  };

  const handleDeleteNode = (e: React.MouseEvent) => {
    e.stopPropagation();
    deleteNode(node.id);
  };

  const handlePortClick = (portId: string, isOutput: boolean, event: React.MouseEvent) => {
    event.stopPropagation();
    event.preventDefault();

    alert(`Port clicked: ${portId} (${isOutput ? 'output' : 'input'}) on node ${node.id}`);
    console.log('Port clicked:', { nodeId: node.id, portId, isOutput, connectionCreation });

    if (connectionCreation?.isCreating) {
      // Complete connection creation
      console.log('Completing connection:', {
        sourceNodeId: connectionCreation.sourceNodeId,
        sourcePortId: connectionCreation.sourcePortId,
        isSourceOutput: connectionCreation.isSourceOutput,
        targetNodeId: node.id,
        targetPortId: portId,
        isTargetOutput: isOutput
      });

      const success = completeConnectionCreation(node.id, portId, isOutput);
      console.log('Connection creation result:', success);

      if (!success) {
        console.log('Connection creation failed - validation error');
      }
    } else {
      // Start connection creation
      console.log('Starting connection creation');
      const rect = nodeRef.current?.getBoundingClientRect();
      if (rect) {
        const mousePosition = {
          x: event.clientX,
          y: event.clientY,
        };
        startConnectionCreation(node.id, portId, isOutput, mousePosition);
        console.log('Connection creation started');
      }
    }
  };

  const connections = getNodeConnections(node.id);
  const inputConnections = connections.filter(conn => conn.targetNodeId === node.id);
  const outputConnections = connections.filter(conn => conn.sourceNodeId === node.id);

  return (
    <>
      <div
        ref={(element) => {
          setNodeRef(element);
          nodeRef.current = element;
        }}
        style={{
          ...style,
          position: 'absolute',
          left: node.position.x,
          top: node.position.y,
          zIndex: isDragging ? 1000 : isSelected ? 100 : 1,
        }}
        className={`w-48 bg-white rounded-lg shadow-sm border-2 transition-all ${
          isSelected ? 'border-indigo-500 shadow-lg' : colorClass
        } ${isDragging ? 'opacity-75 scale-105' : ''}`}
        onClick={handleNodeClick}
        onDoubleClick={handleNodeDoubleClick}
        {...attributes}
      >
        {/* Node Header */}
        <div className="p-3 border-b" {...listeners}>
          <div className="flex items-center gap-2">
            <div className={`p-1.5 rounded ${colorClass}`}>
              <Icon className="w-4 h-4" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm text-gray-900 truncate">
                {node.name}
              </div>
              <div className="text-xs text-gray-500 truncate">
                {node.description}
              </div>
            </div>
            <div className="flex items-center gap-1">
              <StatusIcon className={`w-4 h-4 ${statusColor} ${
                node.status === 'running' ? 'animate-spin' : ''
              }`} />
              <button
                onClick={handleDeleteNode}
                className="p-1 rounded hover:bg-red-100 text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                title="Delete node"
              >
                <Trash2 className="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>

        {/* Input Ports */}
        {node.inputs.length > 0 && (
          <div className="px-3 py-2 border-b bg-gray-50">
            <div className="text-xs font-medium text-gray-700 mb-1">Inputs</div>
            <div className="space-y-1">
              {node.inputs.map((input) => {
                const hasConnection = inputConnections.some(conn => conn.targetPortId === input.id);
                
                return (
                  <div
                    key={input.id}
                    className="flex items-center gap-2 text-xs"
                  >
                    <button
                      onClick={(e) => handlePortClick(input.id, false, e)}
                      onMouseDown={(e) => e.stopPropagation()}
                      data-no-drag="true"
                      className={`w-3 h-3 rounded-full border-2 transition-colors ${
                        hasConnection
                          ? 'bg-blue-500 border-blue-500'
                          : 'bg-white border-blue-300 hover:border-blue-500'
                      }`}
                      title={input.description}
                    />
                    <span className={`truncate ${input.required ? 'font-medium' : ''}`}>
                      {input.name}
                    </span>
                    <span className="text-gray-400">({input.type})</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Output Ports */}
        {node.outputs.length > 0 && (
          <div className="px-3 py-2 bg-gray-50">
            <div className="text-xs font-medium text-gray-700 mb-1">Outputs</div>
            <div className="space-y-1">
              {node.outputs.map((output) => {
                const hasConnection = outputConnections.some(conn => conn.sourcePortId === output.id);
                
                return (
                  <div
                    key={output.id}
                    className="flex items-center gap-2 text-xs"
                  >
                    <button
                      onClick={(e) => handlePortClick(output.id, true, e)}
                      onMouseDown={(e) => e.stopPropagation()}
                      data-no-drag="true"
                      className={`w-3 h-3 rounded-full border-2 transition-colors ${
                        hasConnection
                          ? 'bg-green-500 border-green-500'
                          : 'bg-white border-green-300 hover:border-green-500'
                      }`}
                      title={output.description}
                    />
                    <span className="truncate">
                      {output.name}
                    </span>
                    <span className="text-gray-400">({output.type})</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Node Status */}
        {node.status !== 'idle' && (
          <div className="px-3 py-2 border-t">
            <div className={`text-xs ${statusColor} flex items-center gap-1`}>
              <StatusIcon className={`w-3 h-3 ${
                node.status === 'running' ? 'animate-spin' : ''
              }`} />
              <span className="capitalize">{node.status}</span>
              {node.error && (
                <span className="text-red-500 truncate" title={node.error}>
                  - {node.error}
                </span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Configuration Modal */}
      {showConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900">Configure Node</h3>
              <button
                onClick={() => setShowConfig(false)}
                className="p-1 rounded hover:bg-gray-100"
              >
                <XCircle className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={node.name}
                  onChange={(e) => updateNode(node.id, { name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={node.description}
                  onChange={(e) => updateNode(node.id, { description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 h-20 resize-none"
                />
              </div>

              {/* Configuration Fields */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Configuration
                </label>
                <div className="space-y-3">
                  {Object.entries(node.config).map(([key, value]) => (
                    <div key={key}>
                      <label className="block text-xs text-gray-500 mb-1 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </label>
                      {typeof value === 'boolean' ? (
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={(e) => updateNode(node.id, {
                            config: { ...node.config, [key]: e.target.checked }
                          })}
                          className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                        />
                      ) : typeof value === 'number' ? (
                        <input
                          type="number"
                          value={value}
                          onChange={(e) => updateNode(node.id, {
                            config: { ...node.config, [key]: parseFloat(e.target.value) || 0 }
                          })}
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
                      ) : (
                        <input
                          type="text"
                          value={value}
                          onChange={(e) => updateNode(node.id, {
                            config: { ...node.config, [key]: e.target.value }
                          })}
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex gap-2 justify-end mt-6">
              <button
                onClick={() => setShowConfig(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowConfig(false)}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
