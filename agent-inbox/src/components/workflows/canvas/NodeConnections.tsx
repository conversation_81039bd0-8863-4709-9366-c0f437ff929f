"use client";

import React from 'react';
import { WorkflowNode, WorkflowConnection } from '@/store/workflows/workflowStore';

interface NodeConnectionsProps {
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  zoom: number;
}

interface ConnectionPath {
  id: string;
  path: string;
  sourcePoint: { x: number; y: number };
  targetPoint: { x: number; y: number };
  isActive: boolean;
}

export function NodeConnections({ nodes, connections, zoom }: NodeConnectionsProps) {
  const getNodePosition = (nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    return node ? node.position : { x: 0, y: 0 };
  };

  const getPortPosition = (nodeId: string, portId: string, isOutput: boolean) => {
    const nodePos = getNodePosition(nodeId);
    const node = nodes.find(n => n.id === nodeId);
    
    if (!node) return { x: 0, y: 0 };

    // Node dimensions
    const nodeWidth = 192; // w-48 = 192px
    const nodeHeaderHeight = 60; // Approximate header height
    const portHeight = 20; // Approximate port height
    
    let portIndex = 0;
    let yOffset = nodeHeaderHeight;

    if (isOutput) {
      // Find output port index
      portIndex = node.outputs.findIndex(p => p.id === portId);
      
      // Add input section height if it exists
      if (node.inputs.length > 0) {
        yOffset += 40 + (node.inputs.length * portHeight); // Input section height
      }
      
      yOffset += 20 + (portIndex * portHeight); // Output section header + port offset
      
      return {
        x: nodePos.x + nodeWidth, // Right side of node
        y: nodePos.y + yOffset + (portHeight / 2), // Center of port
      };
    } else {
      // Find input port index
      portIndex = node.inputs.findIndex(p => p.id === portId);
      yOffset += 20 + (portIndex * portHeight); // Input section header + port offset
      
      return {
        x: nodePos.x, // Left side of node
        y: nodePos.y + yOffset + (portHeight / 2), // Center of port
      };
    }
  };

  const createBezierPath = (start: { x: number; y: number }, end: { x: number; y: number }) => {
    const dx = end.x - start.x;
    const dy = end.y - start.y;
    
    // Control points for smooth curve
    const cp1x = start.x + Math.max(50, Math.abs(dx) * 0.5);
    const cp1y = start.y;
    const cp2x = end.x - Math.max(50, Math.abs(dx) * 0.5);
    const cp2y = end.y;
    
    return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`;
  };

  const connectionPaths: ConnectionPath[] = connections.map(connection => {
    const sourcePoint = getPortPosition(connection.sourceNodeId, connection.sourcePortId, true);
    const targetPoint = getPortPosition(connection.targetNodeId, connection.targetPortId, false);
    
    return {
      id: connection.id,
      path: createBezierPath(sourcePoint, targetPoint),
      sourcePoint,
      targetPoint,
      isActive: true, // TODO: Implement active state based on execution
    };
  });

  const handleConnectionClick = (connectionId: string) => {
    console.log('Connection clicked:', connectionId);
    // TODO: Implement connection selection
  };

  const handleConnectionDoubleClick = (connectionId: string) => {
    console.log('Connection double-clicked:', connectionId);
    // TODO: Implement connection deletion or configuration
  };

  return (
    <svg
      className="absolute inset-0 pointer-events-none"
      style={{
        width: '100%',
        height: '100%',
        overflow: 'visible',
      }}
    >
      <defs>
        {/* Arrow markers */}
        <marker
          id="arrowhead"
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill="#6366f1"
          />
        </marker>
        
        <marker
          id="arrowhead-active"
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill="#10b981"
          />
        </marker>
        
        <marker
          id="arrowhead-error"
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill="#ef4444"
          />
        </marker>

        {/* Glow filter for active connections */}
        <filter id="glow">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      {/* Connection paths */}
      {connectionPaths.map((connection) => (
        <g key={connection.id}>
          {/* Connection line */}
          <path
            d={connection.path}
            stroke={connection.isActive ? "#10b981" : "#6366f1"}
            strokeWidth={2 / zoom} // Adjust stroke width based on zoom
            fill="none"
            markerEnd={`url(#${connection.isActive ? 'arrowhead-active' : 'arrowhead'})`}
            className="pointer-events-auto cursor-pointer hover:stroke-indigo-700 transition-colors"
            onClick={() => handleConnectionClick(connection.id)}
            onDoubleClick={() => handleConnectionDoubleClick(connection.id)}
            filter={connection.isActive ? "url(#glow)" : undefined}
          />
          
          {/* Invisible wider path for easier clicking */}
          <path
            d={connection.path}
            stroke="transparent"
            strokeWidth={10 / zoom}
            fill="none"
            className="pointer-events-auto cursor-pointer"
            onClick={() => handleConnectionClick(connection.id)}
            onDoubleClick={() => handleConnectionDoubleClick(connection.id)}
          />

          {/* Connection points */}
          <circle
            cx={connection.sourcePoint.x}
            cy={connection.sourcePoint.y}
            r={4 / zoom}
            fill="#10b981"
            className="pointer-events-none"
          />
          
          <circle
            cx={connection.targetPoint.x}
            cy={connection.targetPoint.y}
            r={4 / zoom}
            fill="#6366f1"
            className="pointer-events-none"
          />

          {/* Data flow animation */}
          {connection.isActive && (
            <circle
              r={3 / zoom}
              fill="#10b981"
              className="opacity-75"
            >
              <animateMotion
                dur="2s"
                repeatCount="indefinite"
                path={connection.path}
              />
            </circle>
          )}
        </g>
      ))}

      {/* Temporary connection line (for drag-to-connect) */}
      {/* TODO: Implement temporary connection during drag */}
    </svg>
  );
}
