"use client";

import React, { useState, useEffect } from 'react';
import { Bar<PERSON>hart3, Clock, TrendingUp, AlertCircle, CheckCircle, XCircle, Activity, Zap, Users, Calendar } from 'lucide-react';

interface ExecutionMetric {
  id: string;
  workflowId: string;
  workflowName: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  nodeCount: number;
  successfulNodes: number;
  failedNodes: number;
  errorMessage?: string;
  executedBy: string;
}

interface PerformanceMetric {
  workflowId: string;
  workflowName: string;
  totalExecutions: number;
  successRate: number;
  averageDuration: number;
  lastExecuted: Date;
  popularityScore: number;
}

export default function WorkflowAnalytics() {
  const [activeTab, setActiveTab] = useState<'overview' | 'executions' | 'performance' | 'errors'>('overview');
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  
  // Mock data - in real implementation, this would come from an analytics service
  const [executionMetrics] = useState<ExecutionMetric[]>([
    {
      id: 'exec-1',
      workflowId: 'workflow-1',
      workflowName: 'AI Content Generator (Copy)',
      startTime: new Date(Date.now() - 300000), // 5 minutes ago
      endTime: new Date(Date.now() - 240000), // 4 minutes ago
      duration: 60000, // 1 minute
      status: 'completed',
      nodeCount: 3,
      successfulNodes: 3,
      failedNodes: 0,
      executedBy: '<EMAIL>'
    },
    {
      id: 'exec-2',
      workflowId: 'workflow-2',
      workflowName: 'Data Processing Pipeline',
      startTime: new Date(Date.now() - 600000), // 10 minutes ago
      endTime: new Date(Date.now() - 480000), // 8 minutes ago
      duration: 120000, // 2 minutes
      status: 'completed',
      nodeCount: 4,
      successfulNodes: 4,
      failedNodes: 0,
      executedBy: '<EMAIL>'
    },
    {
      id: 'exec-3',
      workflowId: 'workflow-3',
      workflowName: 'Customer Support Bot',
      startTime: new Date(Date.now() - 900000), // 15 minutes ago
      endTime: new Date(Date.now() - 840000), // 14 minutes ago
      duration: 60000, // 1 minute
      status: 'failed',
      nodeCount: 5,
      successfulNodes: 3,
      failedNodes: 2,
      errorMessage: 'API rate limit exceeded',
      executedBy: '<EMAIL>'
    }
  ]);

  const [performanceMetrics] = useState<PerformanceMetric[]>([
    {
      workflowId: 'workflow-1',
      workflowName: 'AI Content Generator (Copy)',
      totalExecutions: 45,
      successRate: 95.6,
      averageDuration: 58000,
      lastExecuted: new Date(Date.now() - 300000),
      popularityScore: 8.5
    },
    {
      workflowId: 'workflow-2',
      workflowName: 'Data Processing Pipeline',
      totalExecutions: 32,
      successRate: 87.5,
      averageDuration: 125000,
      lastExecuted: new Date(Date.now() - 600000),
      popularityScore: 7.2
    },
    {
      workflowId: 'workflow-3',
      workflowName: 'Customer Support Bot',
      totalExecutions: 28,
      successRate: 78.6,
      averageDuration: 95000,
      lastExecuted: new Date(Date.now() - 900000),
      popularityScore: 6.8
    }
  ]);

  const getOverviewStats = () => {
    const totalExecutions = executionMetrics.length;
    const completedExecutions = executionMetrics.filter(m => m.status === 'completed').length;
    const failedExecutions = executionMetrics.filter(m => m.status === 'failed').length;
    const runningExecutions = executionMetrics.filter(m => m.status === 'running').length;
    
    const successRate = totalExecutions > 0 ? (completedExecutions / totalExecutions) * 100 : 0;
    const avgDuration = executionMetrics
      .filter(m => m.duration)
      .reduce((sum, m) => sum + (m.duration || 0), 0) / Math.max(1, executionMetrics.filter(m => m.duration).length);

    return {
      totalExecutions,
      completedExecutions,
      failedExecutions,
      runningExecutions,
      successRate,
      avgDuration
    };
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString();
  };

  const stats = getOverviewStats();

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Workflow Analytics</h2>
            <p className="text-gray-600">Monitor workflow performance and execution metrics</p>
          </div>
          <div className="flex items-center gap-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="1h">Last Hour</option>
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
            </select>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'executions', label: 'Executions', icon: Activity },
            { id: 'performance', label: 'Performance', icon: TrendingUp },
            { id: 'errors', label: 'Errors', icon: AlertCircle }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6 overflow-auto">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Executions</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalExecutions}</p>
                  </div>
                  <Activity className="w-8 h-8 text-blue-500" />
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Success Rate</p>
                    <p className="text-2xl font-bold text-green-600">{stats.successRate.toFixed(1)}%</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-500" />
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Avg Duration</p>
                    <p className="text-2xl font-bold text-blue-600">{formatDuration(stats.avgDuration)}</p>
                  </div>
                  <Clock className="w-8 h-8 text-blue-500" />
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Failed Executions</p>
                    <p className="text-2xl font-bold text-red-600">{stats.failedExecutions}</p>
                  </div>
                  <XCircle className="w-8 h-8 text-red-500" />
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-lg border">
              <div className="p-6 border-b">
                <h3 className="font-semibold text-gray-900">Recent Executions</h3>
              </div>
              <div className="divide-y">
                {executionMetrics.slice(0, 5).map((metric) => (
                  <div key={metric.id} className="p-6 flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className={`w-3 h-3 rounded-full ${
                        metric.status === 'completed' ? 'bg-green-500' :
                        metric.status === 'failed' ? 'bg-red-500' :
                        metric.status === 'running' ? 'bg-blue-500' : 'bg-gray-500'
                      }`} />
                      <div>
                        <div className="font-medium text-gray-900">{metric.workflowName}</div>
                        <div className="text-sm text-gray-600">
                          {formatTime(metric.startTime)} • {metric.nodeCount} nodes
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {metric.duration ? formatDuration(metric.duration) : 'Running...'}
                      </div>
                      <div className={`text-xs ${
                        metric.status === 'completed' ? 'text-green-600' :
                        metric.status === 'failed' ? 'text-red-600' :
                        'text-blue-600'
                      }`}>
                        {metric.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'executions' && (
          <div className="bg-white rounded-lg border">
            <div className="p-6 border-b">
              <h3 className="font-semibold text-gray-900">Execution History</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Workflow</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nodes</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Started</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {executionMetrics.map((metric) => (
                    <tr key={metric.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-900">{metric.workflowName}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          metric.status === 'completed' ? 'bg-green-100 text-green-800' :
                          metric.status === 'failed' ? 'bg-red-100 text-red-800' :
                          metric.status === 'running' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {metric.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {metric.duration ? formatDuration(metric.duration) : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {metric.successfulNodes}/{metric.nodeCount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatTime(metric.startTime)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {metric.executedBy}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'performance' && (
          <div className="bg-white rounded-lg border">
            <div className="p-6 border-b">
              <h3 className="font-semibold text-gray-900">Workflow Performance</h3>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {performanceMetrics.map((metric) => (
                  <div key={metric.workflowId} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-gray-900">{metric.workflowName}</h4>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">Popularity:</span>
                        <span className="font-medium text-blue-600">{metric.popularityScore}/10</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div>
                        <div className="text-sm text-gray-600">Total Runs</div>
                        <div className="font-semibold text-gray-900">{metric.totalExecutions}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Success Rate</div>
                        <div className={`font-semibold ${
                          metric.successRate >= 90 ? 'text-green-600' :
                          metric.successRate >= 70 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {metric.successRate.toFixed(1)}%
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Avg Duration</div>
                        <div className="font-semibold text-blue-600">{formatDuration(metric.averageDuration)}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Last Run</div>
                        <div className="font-semibold text-gray-900">{formatTime(metric.lastExecuted)}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'errors' && (
          <div className="bg-white rounded-lg border">
            <div className="p-6 border-b">
              <h3 className="font-semibold text-gray-900">Error Analysis</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {executionMetrics.filter(m => m.status === 'failed').map((metric) => (
                  <div key={metric.id} className="border-l-4 border-red-500 bg-red-50 p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-red-900">{metric.workflowName}</h4>
                      <span className="text-sm text-red-600">{formatTime(metric.startTime)}</span>
                    </div>
                    <div className="text-sm text-red-800">
                      <div>Error: {metric.errorMessage}</div>
                      <div>Failed nodes: {metric.failedNodes}/{metric.nodeCount}</div>
                    </div>
                  </div>
                ))}
                {executionMetrics.filter(m => m.status === 'failed').length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
                    <p>No errors in the selected time range</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
