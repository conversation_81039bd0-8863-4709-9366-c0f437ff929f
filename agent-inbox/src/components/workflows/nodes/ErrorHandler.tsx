"use client";

import React, { useState } from 'react';
import { <PERSON>, Settings, AlertTriangle, CheckCircle, XCircle, RefreshCw } from 'lucide-react';

interface ErrorHandlerProps {
  node: {
    id: string;
    name: string;
    data: {
      retryCount: number;
      retryDelay: number;
      errorTypes: string[];
      fallbackValue?: string;
      logErrors: boolean;
      description?: string;
    };
    status: 'idle' | 'running' | 'completed' | 'error' | 'retrying';
  };
  onUpdate: (nodeId: string, updates: any) => void;
  onDelete: (nodeId: string) => void;
}

const errorTypes = [
  { value: 'network', label: 'Network Errors', description: 'Connection timeouts, DNS failures, etc.' },
  { value: 'validation', label: 'Validation Errors', description: 'Invalid input data or format errors' },
  { value: 'authentication', label: 'Authentication Errors', description: 'API key issues, permission denied' },
  { value: 'rate_limit', label: 'Rate Limit Errors', description: 'Too many requests, quota exceeded' },
  { value: 'server', label: 'Server Errors', description: '5xx HTTP status codes' },
  { value: 'timeout', label: 'Timeout Errors', description: 'Request or processing timeouts' },
  { value: 'custom', label: 'Custom Errors', description: 'Application-specific error conditions' }
];

export default function ErrorHandler({ node, onUpdate, onDelete }: ErrorHandlerProps) {
  const [showConfig, setShowConfig] = useState(false);
  const [retryCount, setRetryCount] = useState(node.data.retryCount || 3);
  const [retryDelay, setRetryDelay] = useState(node.data.retryDelay || 1000);
  const [selectedErrorTypes, setSelectedErrorTypes] = useState(node.data.errorTypes || ['network', 'timeout']);
  const [fallbackValue, setFallbackValue] = useState(node.data.fallbackValue || '');
  const [logErrors, setLogErrors] = useState(node.data.logErrors !== false);

  const handleSave = () => {
    onUpdate(node.id, {
      data: {
        ...node.data,
        retryCount,
        retryDelay,
        errorTypes: selectedErrorTypes,
        fallbackValue,
        logErrors
      }
    });
    setShowConfig(false);
  };

  const toggleErrorType = (errorType: string) => {
    setSelectedErrorTypes(prev => 
      prev.includes(errorType) 
        ? prev.filter(t => t !== errorType)
        : [...prev, errorType]
    );
  };

  const getStatusIcon = () => {
    switch (node.status) {
      case 'running':
        return <Shield className="w-4 h-4 text-blue-500 animate-pulse" />;
      case 'retrying':
        return <RefreshCw className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Shield className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (node.status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'retrying':
        return 'border-yellow-500 bg-yellow-50';
      case 'completed':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  return (
    <>
      <div className={`relative bg-white border-2 rounded-lg p-4 min-w-[200px] ${getStatusColor()}`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <h3 className="font-medium text-gray-900">{node.name}</h3>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={() => setShowConfig(true)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="Configure error handling"
            >
              <Settings className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(node.id)}
              className="p-1 text-gray-400 hover:text-red-600 rounded"
              title="Delete node"
            >
              <XCircle className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Configuration Summary */}
        <div className="mb-4 space-y-2">
          <div className="text-xs text-gray-500">Error Handling</div>
          <div className="text-sm space-y-1">
            <div>Retries: {retryCount}</div>
            <div>Delay: {retryDelay}ms</div>
            <div>Types: {selectedErrorTypes.length} configured</div>
          </div>
        </div>

        {/* Input Port */}
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1/2">
          <div className="w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-sm" title="Input" />
        </div>

        {/* Output Ports */}
        <div className="flex justify-between items-center">
          {/* Success Output */}
          <div className="relative">
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2">
              <div className="w-3 h-3 bg-green-500 rounded-full border-2 border-white shadow-sm" title="Success output" />
            </div>
            <div className="text-xs text-green-600 font-medium pr-4">Success</div>
          </div>

          {/* Error Output */}
          <div className="relative">
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2">
              <div className="w-3 h-3 bg-red-500 rounded-full border-2 border-white shadow-sm" title="Error output" />
            </div>
            <div className="text-xs text-red-600 font-medium pr-4">Error</div>
          </div>
        </div>

        {/* Status Indicator */}
        {(node.status === 'running' || node.status === 'retrying') && (
          <div className="absolute -top-1 -right-1">
            <div className={`w-3 h-3 rounded-full ${
              node.status === 'retrying' ? 'bg-yellow-500 animate-pulse' : 'bg-blue-500 animate-pulse'
            }`} />
          </div>
        )}
      </div>

      {/* Configuration Modal */}
      {showConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] overflow-auto">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">Configure Error Handler</h2>
                <button
                  onClick={() => setShowConfig(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Retry Configuration */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Retry Count
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="10"
                    value={retryCount}
                    onChange={(e) => setRetryCount(parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="mt-1 text-xs text-gray-500">
                    Number of retry attempts (0-10)
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Retry Delay (ms)
                  </label>
                  <input
                    type="number"
                    min="100"
                    max="60000"
                    step="100"
                    value={retryDelay}
                    onChange={(e) => setRetryDelay(parseInt(e.target.value) || 1000)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="mt-1 text-xs text-gray-500">
                    Delay between retries (100-60000ms)
                  </div>
                </div>
              </div>

              {/* Error Types */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Handle Error Types
                </label>
                <div className="space-y-2">
                  {errorTypes.map((errorType) => (
                    <label key={errorType.value} className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedErrorTypes.includes(errorType.value)}
                        onChange={() => toggleErrorType(errorType.value)}
                        className="mt-1"
                      />
                      <div>
                        <div className="font-medium text-gray-900">{errorType.label}</div>
                        <div className="text-sm text-gray-600">{errorType.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Fallback Value */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Fallback Value (Optional)
                </label>
                <textarea
                  value={fallbackValue}
                  onChange={(e) => setFallbackValue(e.target.value)}
                  placeholder='{"status": "error", "message": "Operation failed after retries"}'
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                  rows={3}
                />
                <div className="mt-1 text-xs text-gray-500">
                  JSON value to return when all retries are exhausted. Leave empty to pass through the error.
                </div>
              </div>

              {/* Options */}
              <div>
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={logErrors}
                    onChange={(e) => setLogErrors(e.target.checked)}
                  />
                  <div>
                    <div className="font-medium text-gray-900">Log Errors</div>
                    <div className="text-sm text-gray-600">Log error details for debugging and monitoring</div>
                  </div>
                </label>
              </div>

              {/* Examples */}
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-2 flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4" />
                  Error Handling Strategy
                </h4>
                <div className="space-y-2 text-sm text-orange-800">
                  <div><strong>1.</strong> Input is processed by the wrapped operation</div>
                  <div><strong>2.</strong> If an error occurs, check if it matches selected error types</div>
                  <div><strong>3.</strong> If matched, retry up to the configured count with delays</div>
                  <div><strong>4.</strong> If all retries fail, output fallback value or error to error port</div>
                  <div><strong>5.</strong> If successful at any point, output result to success port</div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t bg-gray-50 flex justify-end gap-3">
              <button
                onClick={() => setShowConfig(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Save Configuration
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
