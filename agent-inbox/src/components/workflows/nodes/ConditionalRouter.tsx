"use client";

import React, { useState } from 'react';
import { <PERSON>itBranch, <PERSON><PERSON><PERSON>, Play, AlertCircle, CheckCircle, XCircle } from 'lucide-react';

interface ConditionalRouterProps {
  node: {
    id: string;
    name: string;
    data: {
      condition: string;
      conditionType: 'javascript' | 'simple' | 'regex';
      trueLabel: string;
      falseLabel: string;
      description?: string;
    };
    status: 'idle' | 'running' | 'completed' | 'error';
  };
  onUpdate: (nodeId: string, updates: any) => void;
  onDelete: (nodeId: string) => void;
}

const conditionTypes = [
  { value: 'simple', label: 'Simple Comparison', description: 'Basic comparisons like equals, contains, greater than' },
  { value: 'javascript', label: 'JavaScript Expression', description: 'Custom JavaScript expressions for complex logic' },
  { value: 'regex', label: 'Regular Expression', description: 'Pattern matching using regular expressions' }
];

const simpleOperators = [
  { value: 'equals', label: 'Equals', symbol: '==' },
  { value: 'not_equals', label: 'Not Equals', symbol: '!=' },
  { value: 'contains', label: 'Contains', symbol: 'contains' },
  { value: 'starts_with', label: 'Starts With', symbol: 'starts with' },
  { value: 'ends_with', label: 'Ends With', symbol: 'ends with' },
  { value: 'greater_than', label: 'Greater Than', symbol: '>' },
  { value: 'less_than', label: 'Less Than', symbol: '<' },
  { value: 'is_empty', label: 'Is Empty', symbol: 'is empty' },
  { value: 'is_not_empty', label: 'Is Not Empty', symbol: 'is not empty' }
];

export default function ConditionalRouter({ node, onUpdate, onDelete }: ConditionalRouterProps) {
  const [showConfig, setShowConfig] = useState(false);
  const [conditionType, setConditionType] = useState(node.data.conditionType || 'simple');
  const [condition, setCondition] = useState(node.data.condition || '');
  const [trueLabel, setTrueLabel] = useState(node.data.trueLabel || 'True');
  const [falseLabel, setFalseLabel] = useState(node.data.falseLabel || 'False');

  const handleSave = () => {
    onUpdate(node.id, {
      data: {
        ...node.data,
        condition,
        conditionType,
        trueLabel,
        falseLabel
      }
    });
    setShowConfig(false);
  };

  const getStatusIcon = () => {
    switch (node.status) {
      case 'running':
        return <Play className="w-4 h-4 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <GitBranch className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (node.status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'completed':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  return (
    <>
      <div className={`relative bg-white border-2 rounded-lg p-4 min-w-[200px] ${getStatusColor()}`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <h3 className="font-medium text-gray-900">{node.name}</h3>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={() => setShowConfig(true)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="Configure condition"
            >
              <Settings className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(node.id)}
              className="p-1 text-gray-400 hover:text-red-600 rounded"
              title="Delete node"
            >
              <XCircle className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Condition Display */}
        <div className="mb-4">
          <div className="text-xs text-gray-500 mb-1">Condition</div>
          <div className="text-sm bg-gray-50 p-2 rounded border font-mono">
            {condition || 'No condition set'}
          </div>
        </div>

        {/* Input Port */}
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1/2">
          <div className="w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-sm" title="Input" />
        </div>

        {/* Output Ports */}
        <div className="flex justify-between items-center">
          {/* True Output */}
          <div className="relative">
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2">
              <div className="w-3 h-3 bg-green-500 rounded-full border-2 border-white shadow-sm" title="True output" />
            </div>
            <div className="text-xs text-green-600 font-medium pr-4">{trueLabel}</div>
          </div>

          {/* False Output */}
          <div className="relative">
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2">
              <div className="w-3 h-3 bg-red-500 rounded-full border-2 border-white shadow-sm" title="False output" />
            </div>
            <div className="text-xs text-red-600 font-medium pr-4">{falseLabel}</div>
          </div>
        </div>

        {/* Status Indicator */}
        {node.status === 'running' && (
          <div className="absolute -top-1 -right-1">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
          </div>
        )}
      </div>

      {/* Configuration Modal */}
      {showConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] overflow-auto">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">Configure Conditional Router</h2>
                <button
                  onClick={() => setShowConfig(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Condition Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Condition Type
                </label>
                <div className="space-y-2">
                  {conditionTypes.map((type) => (
                    <label key={type.value} className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <input
                        type="radio"
                        name="conditionType"
                        value={type.value}
                        checked={conditionType === type.value}
                        onChange={(e) => setConditionType(e.target.value as any)}
                        className="mt-1"
                      />
                      <div>
                        <div className="font-medium text-gray-900">{type.label}</div>
                        <div className="text-sm text-gray-600">{type.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Simple Condition Builder */}
              {conditionType === 'simple' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Build Condition
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      <input
                        type="text"
                        placeholder="input.value"
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        {simpleOperators.map((op) => (
                          <option key={op.value} value={op.value}>{op.label}</option>
                        ))}
                      </select>
                      <input
                        type="text"
                        placeholder="expected value"
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Condition Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {conditionType === 'javascript' ? 'JavaScript Expression' : 
                   conditionType === 'regex' ? 'Regular Expression' : 'Condition'}
                </label>
                <textarea
                  value={condition}
                  onChange={(e) => setCondition(e.target.value)}
                  placeholder={
                    conditionType === 'javascript' ? 'input.value > 10 && input.type === "number"' :
                    conditionType === 'regex' ? '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' :
                    'input.value == "expected"'
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                  rows={3}
                />
                <div className="mt-1 text-xs text-gray-500">
                  Use 'input' to reference the incoming data. Available properties: input.value, input.type, input.metadata
                </div>
              </div>

              {/* Output Labels */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    True Output Label
                  </label>
                  <input
                    type="text"
                    value={trueLabel}
                    onChange={(e) => setTrueLabel(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    False Output Label
                  </label>
                  <input
                    type="text"
                    value={falseLabel}
                    onChange={(e) => setFalseLabel(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Examples */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Examples</h4>
                <div className="space-y-2 text-sm text-blue-800">
                  <div><strong>Simple:</strong> input.value == "approved"</div>
                  <div><strong>JavaScript:</strong> input.value > 100 && input.type === "number"</div>
                  <div><strong>Regex:</strong> /^[A-Z]{2,3}-\d{4}$/.test(input.value)</div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t bg-gray-50 flex justify-end gap-3">
              <button
                onClick={() => setShowConfig(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Save Configuration
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
