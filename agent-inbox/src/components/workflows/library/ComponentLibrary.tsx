"use client";

import React, { useState } from 'react';
import { useDraggable } from '@dnd-kit/core';
import {
  Grid,
  Layers,
  Zap,
  ArrowRight,
  Search,
  Filter,
  Type,
  Bot,
  FileText,
  Database,
  Code,
  Workflow,
  ChevronDown,
  ChevronRight,
  GitBranch,
  Shield
} from 'lucide-react';
import { useWorkflowStore, ComponentDefinition } from '@/store/workflows/workflowStore';

interface ComponentLibraryProps {
  className?: string;
}

const categoryIcons = {
  input: Grid,
  output: FileText,
  processing: Layers,
  ai: Zap,
  integration: ArrowRight,
  logic: GitBranch,
};

const categoryColors = {
  input: 'text-blue-600 bg-blue-100',
  output: 'text-green-600 bg-green-100',
  processing: 'text-orange-600 bg-orange-100',
  ai: 'text-purple-600 bg-purple-100',
  integration: 'text-indigo-600 bg-indigo-100',
  logic: 'text-red-600 bg-red-100',
};

const componentIcons: Record<string, any> = {
  'text-input': Type,
  'openai-chat': Bot,
  'text-output': FileText,
  'data-filter': Filter,
  'database-query': Database,
  'code-executor': Code,
  'workflow-trigger': Workflow,
  'conditional-router': GitBranch,
  'error-handler': Shield,
};

interface DraggableComponentProps {
  component: ComponentDefinition;
}

function DraggableComponent({ component }: DraggableComponentProps) {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: component.id,
    data: { component },
  });

  const Icon = componentIcons[component.id] || Grid;
  const colorClass = categoryColors[component.category];

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`p-3 border rounded-lg cursor-grab hover:shadow-md transition-all ${
        isDragging ? 'opacity-50 shadow-lg scale-105' : 'hover:bg-gray-50'
      }`}
    >
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg ${colorClass}`}>
          <Icon className="w-4 h-4" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm text-gray-900 truncate">
            {component.name}
          </div>
          <div className="text-xs text-gray-500 truncate">
            {component.description}
          </div>
        </div>
      </div>
      
      {/* Port indicators */}
      <div className="flex items-center justify-between mt-2 text-xs text-gray-400">
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-blue-400 rounded-full" />
          <span>{component.inputs.length} in</span>
        </div>
        <div className="flex items-center gap-1">
          <span>{component.outputs.length} out</span>
          <div className="w-2 h-2 bg-green-400 rounded-full" />
        </div>
      </div>
    </div>
  );
}

export function ComponentLibrary({ className }: ComponentLibraryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['input', 'ai', 'output'])
  );
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const { componentDefinitions, showComponentLibrary, toggleComponentLibrary } = useWorkflowStore();

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const filteredComponents = componentDefinitions.filter(component => {
    const matchesSearch = component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         component.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || component.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const groupedComponents = filteredComponents.reduce((acc, component) => {
    if (!acc[component.category]) {
      acc[component.category] = [];
    }
    acc[component.category].push(component);
    return acc;
  }, {} as Record<string, ComponentDefinition[]>);

  const categories = Object.keys(groupedComponents).sort();

  if (!showComponentLibrary) {
    return (
      <div className={`w-12 bg-white border-r flex flex-col items-center py-4 ${className}`}>
        <button
          onClick={toggleComponentLibrary}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          title="Show Component Library"
        >
          <Grid className="w-5 h-5 text-gray-600" />
        </button>
      </div>
    );
  }

  return (
    <div className={`w-80 bg-white border-r flex flex-col ${className}`}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-900">Component Library</h3>
          <button
            onClick={toggleComponentLibrary}
            className="p-1 rounded hover:bg-gray-100 transition-colors"
            title="Hide Component Library"
          >
            <ChevronDown className="w-4 h-4 text-gray-500" />
          </button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
          />
        </div>
      </div>

      {/* Category Filter */}
      <div className="p-4 border-b">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory(null)}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              !selectedCategory
                ? 'bg-indigo-100 text-indigo-700'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          {categories.map((category) => {
            const Icon = categoryIcons[category as keyof typeof categoryIcons];
            const colorClass = categoryColors[category as keyof typeof categoryColors];
            
            return (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`flex items-center gap-1 px-3 py-1 text-xs rounded-full transition-colors ${
                  selectedCategory === category
                    ? colorClass
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Icon className="w-3 h-3" />
                {category}
                <span className="ml-1 bg-white bg-opacity-50 px-1 rounded">
                  {groupedComponents[category]?.length || 0}
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Components */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-4">
          {categories.map((category) => {
            const components = groupedComponents[category] || [];
            const Icon = categoryIcons[category as keyof typeof categoryIcons];
            const isExpanded = expandedCategories.has(category);
            
            if (components.length === 0) return null;
            
            return (
              <div key={category} className="space-y-2">
                <button
                  onClick={() => toggleCategory(category)}
                  className="flex items-center gap-2 w-full p-2 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {isExpanded ? (
                    <ChevronDown className="w-4 h-4 text-gray-500" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-gray-500" />
                  )}
                  <Icon className="w-4 h-4 text-gray-600" />
                  <span className="font-medium text-sm text-gray-900 capitalize">
                    {category}
                  </span>
                  <span className="ml-auto text-xs text-gray-500">
                    {components.length}
                  </span>
                </button>
                
                {isExpanded && (
                  <div className="space-y-2 ml-6">
                    {components.map((component) => (
                      <DraggableComponent
                        key={component.id}
                        component={component}
                      />
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          Drag components to canvas to build workflows
        </div>
        <div className="flex items-center justify-center gap-4 mt-2 text-xs text-gray-400">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-blue-400 rounded-full" />
            <span>Input</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-400 rounded-full" />
            <span>Output</span>
          </div>
        </div>
      </div>
    </div>
  );
}
