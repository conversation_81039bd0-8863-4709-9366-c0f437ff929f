"use client";

import React, { useState } from 'react';
import { FormModal } from '@/components/ui/UniversalModal';
import { Workflow, Layers, Zap, Database, MessageSquare, FileText, Bot } from 'lucide-react';

interface NewWorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateWorkflow: (data: WorkflowFormData) => void | Promise<void>;
}

interface WorkflowFormData {
  name: string;
  description: string;
  category: string;
  tags: string[];
  isPublic: boolean;
}

const workflowCategories = [
  { id: 'ai', name: 'AI & Machine Learning', icon: Bot, description: 'AI models, prompts, and intelligent processing' },
  { id: 'automation', name: 'Automation', icon: Zap, description: 'Task automation and workflow orchestration' },
  { id: 'data', name: 'Data Processing', icon: Database, description: 'Data transformation, analysis, and storage' },
  { id: 'communication', name: 'Communication', icon: MessageSquare, description: 'Messaging, notifications, and integrations' },
  { id: 'productivity', name: 'Productivity', icon: FileText, description: 'Document processing and productivity tools' },
  { id: 'general', name: 'General', icon: Layers, description: 'General purpose workflows' }
];

const commonTags = [
  'chatbot', 'rag', 'automation', 'api', 'data-processing', 'nlp', 
  'document-analysis', 'web-scraping', 'email', 'notification',
  'integration', 'ai-agent', 'prompt-engineering', 'vector-search'
];

export default function NewWorkflowModal({ isOpen, onClose, onCreateWorkflow }: NewWorkflowModalProps) {
  const [selectedCategory, setSelectedCategory] = useState('general');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [customTag, setCustomTag] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (formData: any) => {
    setIsSubmitting(true);
    try {
      const workflowData: WorkflowFormData = {
        name: formData.name,
        description: formData.description,
        category: selectedCategory,
        tags: selectedTags,
        isPublic: formData.isPublic === 'on'
      };
      
      await onCreateWorkflow(workflowData);
      
      // Reset form
      setSelectedCategory('general');
      setSelectedTags([]);
      setCustomTag('');
      onClose();
    } catch (error) {
      console.error('Failed to create workflow:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddCustomTag = () => {
    if (customTag.trim() && !selectedTags.includes(customTag.trim())) {
      setSelectedTags([...selectedTags, customTag.trim()]);
      setCustomTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setSelectedTags(selectedTags.filter(tag => tag !== tagToRemove));
  };

  const handleTagToggle = (tag: string) => {
    if (selectedTags.includes(tag)) {
      handleRemoveTag(tag);
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={handleSubmit}
      title="Create New Workflow"
      description="Build a new workflow from scratch or start with a template"
      size="lg"
      submitLabel="Create Workflow"
      isSubmitting={isSubmitting}
      formId="new-workflow-form"
    >
      <div className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Workflow Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              placeholder="Enter workflow name..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              placeholder="Describe what this workflow does..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            />
          </div>
        </div>

        {/* Category Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Category
          </label>
          <div className="grid grid-cols-2 gap-3">
            {workflowCategories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  type="button"
                  onClick={() => setSelectedCategory(category.id)}
                  className={`p-3 border rounded-lg text-left transition-colors ${
                    selectedCategory === category.id
                      ? 'border-red-500 bg-red-50 text-red-700'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <Icon className="w-4 h-4" />
                    <span className="font-medium text-sm">{category.name}</span>
                  </div>
                  <p className="text-xs text-gray-600">{category.description}</p>
                </button>
              );
            })}
          </div>
        </div>

        {/* Tags */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Tags
          </label>
          
          {/* Selected Tags */}
          {selectedTags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-3">
              {selectedTags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    className="text-red-500 hover:text-red-700"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}

          {/* Common Tags */}
          <div className="mb-3">
            <p className="text-xs text-gray-600 mb-2">Common tags:</p>
            <div className="flex flex-wrap gap-2">
              {commonTags.map((tag) => (
                <button
                  key={tag}
                  type="button"
                  onClick={() => handleTagToggle(tag)}
                  className={`px-2 py-1 text-xs rounded-full border transition-colors ${
                    selectedTags.includes(tag)
                      ? 'bg-red-100 text-red-700 border-red-300'
                      : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Tag Input */}
          <div className="flex gap-2">
            <input
              type="text"
              value={customTag}
              onChange={(e) => setCustomTag(e.target.value)}
              placeholder="Add custom tag..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddCustomTag();
                }
              }}
            />
            <button
              type="button"
              onClick={handleAddCustomTag}
              className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
            >
              Add
            </button>
          </div>
        </div>

        {/* Settings */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Settings
          </label>
          <div className="space-y-2">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                name="isPublic"
                className="rounded border-gray-300 text-red-600 focus:ring-red-500"
              />
              <span className="text-sm text-gray-700">Make this workflow public</span>
            </label>
          </div>
        </div>

        {/* Quick Start Options */}
        <div className="border-t pt-4">
          <p className="text-sm font-medium text-gray-700 mb-3">Quick Start</p>
          <div className="grid grid-cols-2 gap-3">
            <button
              type="button"
              className="p-3 border border-gray-300 rounded-lg hover:border-gray-400 transition-colors text-left"
            >
              <div className="flex items-center gap-2 mb-1">
                <Workflow className="w-4 h-4 text-blue-600" />
                <span className="font-medium text-sm">Blank Canvas</span>
              </div>
              <p className="text-xs text-gray-600">Start with an empty workflow</p>
            </button>
            <button
              type="button"
              className="p-3 border border-gray-300 rounded-lg hover:border-gray-400 transition-colors text-left"
            >
              <div className="flex items-center gap-2 mb-1">
                <Layers className="w-4 h-4 text-purple-600" />
                <span className="font-medium text-sm">From Template</span>
              </div>
              <p className="text-xs text-gray-600">Choose from pre-built templates</p>
            </button>
          </div>
        </div>
      </div>
    </FormModal>
  );
}
