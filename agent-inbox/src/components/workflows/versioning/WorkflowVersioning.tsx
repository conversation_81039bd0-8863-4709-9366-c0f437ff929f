"use client";

import React, { useState } from 'react';
import { GitBranch, Clock, User, Tag, Plus, RotateCcw, Eye, Download, Upload } from 'lucide-react';

interface WorkflowVersion {
  id: string;
  version: string;
  name: string;
  description: string;
  createdAt: Date;
  createdBy: string;
  isActive: boolean;
  changes: string[];
  nodeCount: number;
  connectionCount: number;
  tags: string[];
}

interface WorkflowVersioningProps {
  workflowId: string;
  workflowName: string;
  onVersionRestore: (versionId: string) => void;
  onVersionCreate: (version: Omit<WorkflowVersion, 'id' | 'createdAt'>) => void;
}

export default function WorkflowVersioning({ 
  workflowId, 
  workflowName, 
  onVersionRestore, 
  onVersionCreate 
}: WorkflowVersioningProps) {
  const [showCreateVersion, setShowCreateVersion] = useState(false);
  const [newVersionName, setNewVersionName] = useState('');
  const [newVersionDescription, setNewVersionDescription] = useState('');
  const [newVersionTags, setNewVersionTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  // Mock version data - in real implementation, this would come from a versioning service
  const [versions] = useState<WorkflowVersion[]>([
    {
      id: 'v1',
      version: '1.0.0',
      name: 'Initial Version',
      description: 'First working version of the workflow',
      createdAt: new Date(Date.now() - 86400000 * 7), // 7 days ago
      createdBy: '<EMAIL>',
      isActive: false,
      changes: ['Created initial workflow structure', 'Added basic AI content generation'],
      nodeCount: 3,
      connectionCount: 2,
      tags: ['stable', 'production']
    },
    {
      id: 'v2',
      version: '1.1.0',
      name: 'Enhanced Processing',
      description: 'Added error handling and improved performance',
      createdAt: new Date(Date.now() - 86400000 * 3), // 3 days ago
      createdBy: '<EMAIL>',
      isActive: false,
      changes: [
        'Added error handling node',
        'Improved AI prompt configuration',
        'Added input validation',
        'Performance optimizations'
      ],
      nodeCount: 5,
      connectionCount: 4,
      tags: ['enhanced', 'error-handling']
    },
    {
      id: 'v3',
      version: '1.2.0',
      name: 'Current Version',
      description: 'Latest version with conditional logic and advanced features',
      createdAt: new Date(Date.now() - 86400000), // 1 day ago
      createdBy: '<EMAIL>',
      isActive: true,
      changes: [
        'Added conditional router',
        'Implemented workflow templates',
        'Enhanced analytics tracking',
        'UI improvements'
      ],
      nodeCount: 7,
      connectionCount: 6,
      tags: ['current', 'advanced', 'templates']
    }
  ]);

  const handleCreateVersion = () => {
    if (!newVersionName.trim()) return;

    const newVersion: Omit<WorkflowVersion, 'id' | 'createdAt'> = {
      version: `1.${versions.length}.0`,
      name: newVersionName,
      description: newVersionDescription,
      createdBy: '<EMAIL>',
      isActive: false,
      changes: ['Manual version creation'],
      nodeCount: 0, // Would be calculated from current workflow
      connectionCount: 0, // Would be calculated from current workflow
      tags: newVersionTags
    };

    onVersionCreate(newVersion);
    setShowCreateVersion(false);
    setNewVersionName('');
    setNewVersionDescription('');
    setNewVersionTags([]);
  };

  const addTag = () => {
    if (tagInput.trim() && !newVersionTags.includes(tagInput.trim())) {
      setNewVersionTags([...newVersionTags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setNewVersionTags(newVersionTags.filter(tag => tag !== tagToRemove));
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const getVersionColor = (version: WorkflowVersion) => {
    if (version.isActive) return 'border-green-500 bg-green-50';
    if (version.tags.includes('production')) return 'border-blue-500 bg-blue-50';
    return 'border-gray-300 bg-white';
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Workflow Versions</h2>
            <p className="text-gray-600">{workflowName}</p>
          </div>
          <button
            onClick={() => setShowCreateVersion(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Create Version
          </button>
        </div>

        {/* Version Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <GitBranch className="w-5 h-5 text-blue-500" />
              <span className="font-medium">Total Versions</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 mt-1">{versions.length}</div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <Tag className="w-5 h-5 text-green-500" />
              <span className="font-medium">Current Version</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 mt-1">
              {versions.find(v => v.isActive)?.version || 'None'}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-purple-500" />
              <span className="font-medium">Last Updated</span>
            </div>
            <div className="text-sm text-gray-600 mt-1">
              {versions.length > 0 ? formatDate(versions[versions.length - 1].createdAt) : 'Never'}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <User className="w-5 h-5 text-orange-500" />
              <span className="font-medium">Contributors</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 mt-1">
              {new Set(versions.map(v => v.createdBy)).size}
            </div>
          </div>
        </div>
      </div>

      {/* Version List */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="space-y-4">
          {versions.map((version) => (
            <div key={version.id} className={`border-2 rounded-lg p-6 ${getVersionColor(version)}`}>
              {/* Version Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <GitBranch className="w-5 h-5 text-gray-600" />
                    <span className="font-bold text-lg">{version.version}</span>
                    {version.isActive && (
                      <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                        ACTIVE
                      </span>
                    )}
                  </div>
                  <h3 className="font-semibold text-gray-900">{version.name}</h3>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    className="p-2 text-gray-400 hover:text-gray-600 rounded"
                    title="View version details"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-gray-600 rounded"
                    title="Export version"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                  {!version.isActive && (
                    <button
                      onClick={() => onVersionRestore(version.id)}
                      className="p-2 text-blue-600 hover:text-blue-700 rounded"
                      title="Restore this version"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>

              {/* Version Info */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <div className="text-sm text-gray-600">Created</div>
                  <div className="font-medium">{formatDate(version.createdAt)}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Author</div>
                  <div className="font-medium">{version.createdBy}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Complexity</div>
                  <div className="font-medium">{version.nodeCount} nodes, {version.connectionCount} connections</div>
                </div>
              </div>

              {/* Description */}
              {version.description && (
                <div className="mb-4">
                  <div className="text-sm text-gray-600 mb-1">Description</div>
                  <div className="text-gray-900">{version.description}</div>
                </div>
              )}

              {/* Changes */}
              <div className="mb-4">
                <div className="text-sm text-gray-600 mb-2">Changes</div>
                <ul className="space-y-1">
                  {version.changes.map((change, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                      <span>{change}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Tags */}
              {version.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {version.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Create Version Modal */}
      {showCreateVersion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] overflow-auto">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">Create New Version</h2>
                <button
                  onClick={() => setShowCreateVersion(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Version Name *
                </label>
                <input
                  type="text"
                  value={newVersionName}
                  onChange={(e) => setNewVersionName(e.target.value)}
                  placeholder="e.g., Enhanced Error Handling"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={newVersionDescription}
                  onChange={(e) => setNewVersionDescription(e.target.value)}
                  placeholder="Describe the changes in this version..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <div className="flex gap-2 mb-2">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                    placeholder="Add a tag..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    onClick={addTag}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {newVersionTags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded-full"
                    >
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        className="text-blue-500 hover:text-blue-700"
                      >
                        ✕
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </div>

            <div className="p-6 border-t bg-gray-50 flex justify-end gap-3">
              <button
                onClick={() => setShowCreateVersion(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateVersion}
                disabled={!newVersionName.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Create Version
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
