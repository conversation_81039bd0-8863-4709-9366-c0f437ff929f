"use client";

import React, { useState, useEffect } from 'react';
import { Key, Eye, EyeOff, Plus, Trash2, Edit, Save, X, Shield, AlertTriangle } from 'lucide-react';

interface ApiKey {
  id: string;
  name: string;
  service: string;
  key: string;
  masked: string;
  createdAt: Date;
  lastUsed?: Date;
  isActive: boolean;
}

interface ApiKeyManagerProps {
  onKeysChange?: (keys: ApiKey[]) => void;
}

const supportedServices = [
  { id: 'openai', name: 'OpenAI', description: 'GPT models and embeddings' },
  { id: 'anthropic', name: 'Anthropic', description: 'Claude models' },
  { id: 'google', name: 'Google', description: 'Gemini and Vertex AI' },
  { id: 'pinecone', name: 'Pinecone', description: 'Vector database' },
  { id: 'huggingface', name: 'Hugging Face', description: 'Open source models' },
  { id: 'cohere', name: 'Cohere', description: 'Language models' },
  { id: 'azure', name: 'Azure OpenAI', description: 'Microsoft Azure AI services' },
  { id: 'aws', name: 'AWS', description: 'Amazon Web Services' },
  { id: 'custom', name: 'Custom API', description: 'Custom API endpoint' }
];

export default function ApiKeyManager({ onKeysChange }: ApiKeyManagerProps) {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set());
  const [newKey, setNewKey] = useState({
    name: '',
    service: '',
    key: ''
  });

  useEffect(() => {
    // Load API keys from localStorage
    const savedKeys = localStorage.getItem('workflow-api-keys');
    if (savedKeys) {
      try {
        const keys = JSON.parse(savedKeys);
        setApiKeys(keys);
      } catch (error) {
        console.error('Failed to load API keys:', error);
      }
    }
  }, []);

  useEffect(() => {
    // Save API keys to localStorage
    localStorage.setItem('workflow-api-keys', JSON.stringify(apiKeys));
    onKeysChange?.(apiKeys);
  }, [apiKeys, onKeysChange]);

  const maskApiKey = (key: string): string => {
    if (key.length <= 8) return '*'.repeat(key.length);
    return key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);
  };

  const addApiKey = () => {
    if (!newKey.name || !newKey.service || !newKey.key) return;

    const apiKey: ApiKey = {
      id: `key-${Date.now()}`,
      name: newKey.name,
      service: newKey.service,
      key: newKey.key,
      masked: maskApiKey(newKey.key),
      createdAt: new Date(),
      isActive: true
    };

    setApiKeys(prev => [...prev, apiKey]);
    setNewKey({ name: '', service: '', key: '' });
    setShowAddForm(false);
  };

  const deleteApiKey = (id: string) => {
    setApiKeys(prev => prev.filter(key => key.id !== id));
    setVisibleKeys(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  const toggleKeyVisibility = (id: string) => {
    setVisibleKeys(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const toggleKeyStatus = (id: string) => {
    setApiKeys(prev => prev.map(key => 
      key.id === id ? { ...key, isActive: !key.isActive } : key
    ));
  };

  const getServiceInfo = (serviceId: string) => {
    return supportedServices.find(s => s.id === serviceId) || { name: serviceId, description: '' };
  };

  return (
    <div className="bg-white rounded-lg border">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Key className="w-6 h-6 text-red-600" />
            <div>
              <h3 className="text-xl font-semibold text-gray-900">API Key Management</h3>
              <p className="text-gray-600">Manage API keys for external services</p>
            </div>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add API Key
          </button>
        </div>
      </div>

      {/* Security Notice */}
      <div className="p-4 bg-yellow-50 border-b border-yellow-200">
        <div className="flex items-start gap-3">
          <Shield className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-yellow-800">Security Notice</p>
            <p className="text-yellow-700">
              API keys are stored locally in your browser. For production use, consider using environment variables or a secure key management service.
            </p>
          </div>
        </div>
      </div>

      {/* Add Key Form */}
      {showAddForm && (
        <div className="p-6 border-b bg-gray-50">
          <h4 className="font-medium text-gray-900 mb-4">Add New API Key</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Key Name
              </label>
              <input
                type="text"
                value={newKey.name}
                onChange={(e) => setNewKey(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., OpenAI Production Key"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service
              </label>
              <select
                value={newKey.service}
                onChange={(e) => setNewKey(prev => ({ ...prev, service: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">Select a service</option>
                {supportedServices.map(service => (
                  <option key={service.id} value={service.id}>
                    {service.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API Key
              </label>
              <input
                type="password"
                value={newKey.key}
                onChange={(e) => setNewKey(prev => ({ ...prev, key: e.target.value }))}
                placeholder="Enter your API key"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              />
            </div>
          </div>
          <div className="flex items-center gap-3 mt-4">
            <button
              onClick={addApiKey}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              Save Key
            </button>
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
            >
              <X className="w-4 h-4" />
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* API Keys List */}
      <div className="p-6">
        {apiKeys.length === 0 ? (
          <div className="text-center py-12">
            <Key className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No API Keys</h4>
            <p className="text-gray-600 mb-4">Add your first API key to start using external services</p>
            <button
              onClick={() => setShowAddForm(true)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Add API Key
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {apiKeys.map((apiKey) => {
              const serviceInfo = getServiceInfo(apiKey.service);
              const isVisible = visibleKeys.has(apiKey.id);
              
              return (
                <div key={apiKey.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h5 className="font-medium text-gray-900">{apiKey.name}</h5>
                        <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                          {serviceInfo.name}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          apiKey.isActive 
                            ? 'bg-green-100 text-green-700' 
                            : 'bg-gray-100 text-gray-700'
                        }`}>
                          {apiKey.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div className="flex items-center gap-3 text-sm text-gray-600">
                        <span className="font-mono">
                          {isVisible ? apiKey.key : apiKey.masked}
                        </span>
                        <button
                          onClick={() => toggleKeyVisibility(apiKey.id)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          {isVisible ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Created: {apiKey.createdAt.toLocaleDateString()}
                        {apiKey.lastUsed && (
                          <span className="ml-4">
                            Last used: {apiKey.lastUsed.toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => toggleKeyStatus(apiKey.id)}
                        className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                          apiKey.isActive
                            ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            : 'bg-green-100 text-green-700 hover:bg-green-200'
                        }`}
                      >
                        {apiKey.isActive ? 'Disable' : 'Enable'}
                      </button>
                      <button
                        onClick={() => deleteApiKey(apiKey.id)}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
