"use client";

import React, { useEffect } from 'react';
import { 
  FileTex<PERSON>, 
  Folder, 
  FolderOpen, 
  ChevronRight, 
  ChevronDown,
  File,
  Code,
  Image,
  Settings,
  FileJson,
} from 'lucide-react';
import { useFileSystemStore, FileNode } from '@/store/code/fileSystemStore';
import { useEditorStore } from '@/store/code/editorStore';

interface FileExplorerProps {
  className?: string;
}

const getFileIcon = (fileName: string, type: 'file' | 'folder') => {
  if (type === 'folder') return null;
  
  const ext = fileName.split('.').pop()?.toLowerCase();
  const iconProps = { className: "w-4 h-4" };
  
  switch (ext) {
    case 'tsx':
    case 'ts':
    case 'jsx':
    case 'js':
      return <Code {...iconProps} className="w-4 h-4 text-blue-400" />;
    case 'json':
      return <FileJson {...iconProps} className="w-4 h-4 text-yellow-400" />;
    case 'md':
      return <FileText {...iconProps} className="w-4 h-4 text-gray-400" />;
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
      return <Image {...iconProps} className="w-4 h-4 text-green-400" />;
    case 'css':
    case 'scss':
      return <File {...iconProps} className="w-4 h-4 text-pink-400" />;
    default:
      return <FileText {...iconProps} className="w-4 h-4 text-gray-400" />;
  }
};

interface FileNodeItemProps {
  node: FileNode;
  depth: number;
}

function FileNodeItem({ node, depth }: FileNodeItemProps) {
  const { 
    expandedFolders, 
    toggleFolder, 
    readFile, 
    getLanguageFromPath 
  } = useFileSystemStore();
  const { openFile } = useEditorStore();

  const isExpanded = expandedFolders.has(node.id);
  const paddingLeft = depth * 16 + 8;

  const handleClick = async () => {
    if (node.type === 'folder') {
      toggleFolder(node.id);
    } else {
      // Open file in editor
      try {
        const content = await readFile(node.path);
        const language = getLanguageFromPath(node.path);
        
        openFile({
          name: node.name,
          path: node.path,
          content,
          language,
        });
      } catch (error) {
        console.error('Failed to open file:', error);
      }
    }
  };

  return (
    <div>
      <div
        className="flex items-center py-1 px-2 hover:bg-gray-700 cursor-pointer text-sm text-gray-300 hover:text-white transition-colors"
        style={{ paddingLeft }}
        onClick={handleClick}
      >
        {node.type === 'folder' && (
          <div className="w-4 h-4 mr-1 flex items-center justify-center">
            {isExpanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )}
          </div>
        )}
        
        <div className="w-4 h-4 mr-2 flex items-center justify-center">
          {node.type === 'folder' ? (
            isExpanded ? (
              <FolderOpen className="w-4 h-4 text-blue-400" />
            ) : (
              <Folder className="w-4 h-4 text-blue-400" />
            )
          ) : (
            getFileIcon(node.name, node.type)
          )}
        </div>
        
        <span className="truncate">{node.name}</span>
        
        {node.type === 'file' && node.size && (
          <span className="ml-auto text-xs text-gray-500">
            {formatFileSize(node.size)}
          </span>
        )}
      </div>
      
      {node.type === 'folder' && isExpanded && node.children && (
        <div>
          {node.children.map((child) => (
            <FileNodeItem
              key={child.id}
              node={child}
              depth={depth + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

export function FileExplorer({ className }: FileExplorerProps) {
  const { rootNodes, loadFileTree } = useFileSystemStore();

  useEffect(() => {
    // Load the file tree when component mounts
    loadFileTree();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className={`bg-gray-800 border-r border-gray-700 ${className}`}>
      <div className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <Folder className="w-5 h-5 text-gray-400" />
          <h3 className="font-semibold text-white">Explorer</h3>
        </div>
        
        <div className="space-y-1">
          {rootNodes.map((node) => (
            <FileNodeItem
              key={node.id}
              node={node}
              depth={0}
            />
          ))}
        </div>
        
        {rootNodes.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <Folder className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No files found</p>
          </div>
        )}
      </div>
    </div>
  );
}
