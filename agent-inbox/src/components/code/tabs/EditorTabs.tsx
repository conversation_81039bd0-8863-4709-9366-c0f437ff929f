"use client";

import React from 'react';
import { X, Circle, Code, FileText, FileJson, Image, File } from 'lucide-react';
import { useEditorStore } from '@/store/code/editorStore';

interface EditorTabsProps {
  className?: string;
}

const getFileIcon = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase();
  const iconProps = { className: "w-4 h-4" };
  
  switch (ext) {
    case 'tsx':
    case 'ts':
    case 'jsx':
    case 'js':
      return <Code {...iconProps} className="w-4 h-4 text-blue-400" />;
    case 'json':
      return <FileJson {...iconProps} className="w-4 h-4 text-yellow-400" />;
    case 'md':
      return <FileText {...iconProps} className="w-4 h-4 text-gray-400" />;
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
      return <Image {...iconProps} className="w-4 h-4 text-green-400" />;
    case 'css':
    case 'scss':
      return <File {...iconProps} className="w-4 h-4 text-pink-400" />;
    default:
      return <FileText {...iconProps} className="w-4 h-4 text-gray-400" />;
  }
};

export function EditorTabs({ className }: EditorTabsProps) {
  const { 
    openFiles, 
    activeFileId, 
    setActiveFile, 
    closeFile 
  } = useEditorStore();

  if (openFiles.length === 0) {
    return (
      <div className={`bg-gray-800 border-b border-gray-700 h-10 ${className}`}>
        <div className="flex items-center h-full px-4 text-sm text-gray-500">
          No files open
        </div>
      </div>
    );
  }

  const handleTabClick = (fileId: string) => {
    setActiveFile(fileId);
  };

  const handleCloseTab = (e: React.MouseEvent, fileId: string) => {
    e.stopPropagation();
    closeFile(fileId);
  };

  return (
    <div className={`bg-gray-800 border-b border-gray-700 ${className}`}>
      <div className="flex items-center overflow-x-auto scrollbar-hide">
        {openFiles.map((file) => (
          <div
            key={file.id}
            className={`
              flex items-center gap-2 px-4 py-2 border-r border-gray-600 cursor-pointer
              min-w-0 max-w-48 group transition-colors
              ${file.isActive 
                ? 'bg-gray-700 text-white border-b-2 border-blue-500' 
                : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }
            `}
            onClick={() => handleTabClick(file.id)}
          >
            {/* File icon */}
            <div className="flex-shrink-0">
              {getFileIcon(file.name)}
            </div>
            
            {/* File name */}
            <span className="truncate text-sm font-medium">
              {file.name}
            </span>
            
            {/* Dirty indicator */}
            {file.isDirty && (
              <Circle className="w-2 h-2 fill-current text-orange-400 flex-shrink-0" />
            )}
            
            {/* Close button */}
            <button
              className="flex-shrink-0 p-1 rounded hover:bg-gray-600 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => handleCloseTab(e, file.id)}
              title="Close file"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
