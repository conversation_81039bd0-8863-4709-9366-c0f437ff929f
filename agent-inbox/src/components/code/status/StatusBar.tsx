"use client";

import React from 'react';
import { GitBranch, Zap, Settings } from 'lucide-react';
import { useEditorStore } from '@/store/code/editorStore';

interface StatusBarProps {
  className?: string;
}

export function StatusBar({ className }: StatusBarProps) {
  const { 
    getActiveFile, 
    cursorPosition, 
    settings,
    updateSettings 
  } = useEditorStore();

  const activeFile = getActiveFile();

  const handleThemeToggle = () => {
    const newTheme = settings.theme === 'vs-dark' ? 'vs-light' : 'vs-dark';
    updateSettings({ theme: newTheme });
  };

  return (
    <div className={`flex items-center justify-between px-4 py-2 bg-gray-800 border-t border-gray-700 text-sm text-gray-400 ${className}`}>
      {/* Left side */}
      <div className="flex items-center gap-4">
        {/* Git branch */}
        <div className="flex items-center gap-1">
          <GitBranch className="w-4 h-4" />
          <span>main</span>
        </div>
        
        {/* Language */}
        {activeFile && (
          <div className="capitalize">
            {activeFile.language}
          </div>
        )}
        
        {/* File encoding */}
        <div>UTF-8</div>
        
        {/* Line endings */}
        <div>LF</div>
      </div>

      {/* Right side */}
      <div className="flex items-center gap-4">
        {/* Cursor position */}
        {activeFile && (
          <div>
            Ln {cursorPosition.line}, Col {cursorPosition.column}
          </div>
        )}
        
        {/* Spaces/Tabs */}
        <div>Spaces: 2</div>
        
        {/* Theme toggle */}
        <button
          onClick={handleThemeToggle}
          className="flex items-center gap-1 hover:text-white transition-colors"
          title="Toggle theme"
        >
          <Settings className="w-4 h-4" />
          <span className="capitalize">{settings.theme.replace('vs-', '')}</span>
        </button>
        
        {/* Connection status */}
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>Monaco Editor</span>
        </div>
      </div>
    </div>
  );
}
