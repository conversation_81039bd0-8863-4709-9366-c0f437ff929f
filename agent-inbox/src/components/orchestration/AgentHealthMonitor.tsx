"use client";

import React, { useState, useEffect } from 'react';
import { Heart, Cpu, MemoryStick, HardDrive, Wifi, AlertTriangle, CheckCircle, XCircle, Activity, Zap, Clock } from 'lucide-react';

interface HealthMetric {
  name: string;
  value: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  threshold: {
    warning: number;
    critical: number;
  };
}

interface AgentHealth {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'degraded' | 'maintenance';
  lastHeartbeat: Date;
  uptime: number;
  version: string;
  metrics: {
    cpu: HealthMetric;
    memory: HealthMetric;
    disk: HealthMetric;
    network: HealthMetric;
    responseTime: HealthMetric;
    throughput: HealthMetric;
  };
  alerts: Array<{
    id: string;
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: Date;
  }>;
}

export default function AgentHealthMonitor() {
  const [agents, setAgents] = useState<AgentHealth[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<AgentHealth | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    // Initialize with sample agent health data
    const sampleAgents: AgentHealth[] = [
      {
        id: 'research-agent',
        name: 'Research Agent',
        status: 'online',
        lastHeartbeat: new Date(),
        uptime: 99.8,
        version: '2.1.0',
        metrics: {
          cpu: {
            name: 'CPU Usage',
            value: 45,
            unit: '%',
            status: 'healthy',
            threshold: { warning: 70, critical: 90 }
          },
          memory: {
            name: 'Memory Usage',
            value: 62,
            unit: '%',
            status: 'healthy',
            threshold: { warning: 80, critical: 95 }
          },
          disk: {
            name: 'Disk Usage',
            value: 34,
            unit: '%',
            status: 'healthy',
            threshold: { warning: 85, critical: 95 }
          },
          network: {
            name: 'Network Latency',
            value: 45,
            unit: 'ms',
            status: 'healthy',
            threshold: { warning: 100, critical: 200 }
          },
          responseTime: {
            name: 'Response Time',
            value: 120,
            unit: 'ms',
            status: 'healthy',
            threshold: { warning: 500, critical: 1000 }
          },
          throughput: {
            name: 'Throughput',
            value: 85,
            unit: 'req/min',
            status: 'healthy',
            threshold: { warning: 50, critical: 20 }
          }
        },
        alerts: [
          {
            id: 'alert-1',
            type: 'info',
            message: 'Agent started successfully',
            timestamp: new Date(Date.now() - 3600000)
          }
        ]
      },
      {
        id: 'writing-agent',
        name: 'Writing Agent',
        status: 'degraded',
        lastHeartbeat: new Date(Date.now() - 30000),
        uptime: 97.2,
        version: '2.0.8',
        metrics: {
          cpu: {
            name: 'CPU Usage',
            value: 78,
            unit: '%',
            status: 'warning',
            threshold: { warning: 70, critical: 90 }
          },
          memory: {
            name: 'Memory Usage',
            value: 89,
            unit: '%',
            status: 'warning',
            threshold: { warning: 80, critical: 95 }
          },
          disk: {
            name: 'Disk Usage',
            value: 67,
            unit: '%',
            status: 'healthy',
            threshold: { warning: 85, critical: 95 }
          },
          network: {
            name: 'Network Latency',
            value: 156,
            unit: 'ms',
            status: 'warning',
            threshold: { warning: 100, critical: 200 }
          },
          responseTime: {
            name: 'Response Time',
            value: 780,
            unit: 'ms',
            status: 'warning',
            threshold: { warning: 500, critical: 1000 }
          },
          throughput: {
            name: 'Throughput',
            value: 42,
            unit: 'req/min',
            status: 'warning',
            threshold: { warning: 50, critical: 20 }
          }
        },
        alerts: [
          {
            id: 'alert-2',
            type: 'warning',
            message: 'High memory usage detected',
            timestamp: new Date(Date.now() - 600000)
          },
          {
            id: 'alert-3',
            type: 'warning',
            message: 'Response time degradation',
            timestamp: new Date(Date.now() - 300000)
          }
        ]
      },
      {
        id: 'analysis-agent',
        name: 'Analysis Agent',
        status: 'online',
        lastHeartbeat: new Date(Date.now() - 5000),
        uptime: 99.9,
        version: '2.1.2',
        metrics: {
          cpu: {
            name: 'CPU Usage',
            value: 23,
            unit: '%',
            status: 'healthy',
            threshold: { warning: 70, critical: 90 }
          },
          memory: {
            name: 'Memory Usage',
            value: 41,
            unit: '%',
            status: 'healthy',
            threshold: { warning: 80, critical: 95 }
          },
          disk: {
            name: 'Disk Usage',
            value: 28,
            unit: '%',
            status: 'healthy',
            threshold: { warning: 85, critical: 95 }
          },
          network: {
            name: 'Network Latency',
            value: 32,
            unit: 'ms',
            status: 'healthy',
            threshold: { warning: 100, critical: 200 }
          },
          responseTime: {
            name: 'Response Time',
            value: 89,
            unit: 'ms',
            status: 'healthy',
            threshold: { warning: 500, critical: 1000 }
          },
          throughput: {
            name: 'Throughput',
            value: 92,
            unit: 'req/min',
            status: 'healthy',
            threshold: { warning: 50, critical: 20 }
          }
        },
        alerts: []
      },
      {
        id: 'code-agent',
        name: 'Code Agent',
        status: 'offline',
        lastHeartbeat: new Date(Date.now() - 300000),
        uptime: 94.5,
        version: '2.0.9',
        metrics: {
          cpu: {
            name: 'CPU Usage',
            value: 0,
            unit: '%',
            status: 'critical',
            threshold: { warning: 70, critical: 90 }
          },
          memory: {
            name: 'Memory Usage',
            value: 0,
            unit: '%',
            status: 'critical',
            threshold: { warning: 80, critical: 95 }
          },
          disk: {
            name: 'Disk Usage',
            value: 45,
            unit: '%',
            status: 'healthy',
            threshold: { warning: 85, critical: 95 }
          },
          network: {
            name: 'Network Latency',
            value: 0,
            unit: 'ms',
            status: 'critical',
            threshold: { warning: 100, critical: 200 }
          },
          responseTime: {
            name: 'Response Time',
            value: 0,
            unit: 'ms',
            status: 'critical',
            threshold: { warning: 500, critical: 1000 }
          },
          throughput: {
            name: 'Throughput',
            value: 0,
            unit: 'req/min',
            status: 'critical',
            threshold: { warning: 50, critical: 20 }
          }
        },
        alerts: [
          {
            id: 'alert-4',
            type: 'error',
            message: 'Agent connection lost',
            timestamp: new Date(Date.now() - 300000)
          }
        ]
      }
    ];

    setAgents(sampleAgents);
    setSelectedAgent(sampleAgents[0]);
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // Simulate real-time updates
      setAgents(prev => prev.map(agent => ({
        ...agent,
        lastHeartbeat: agent.status === 'online' ? new Date() : agent.lastHeartbeat,
        metrics: {
          ...agent.metrics,
          cpu: {
            ...agent.metrics.cpu,
            value: agent.status === 'online' ? 
              Math.max(0, Math.min(100, agent.metrics.cpu.value + (Math.random() - 0.5) * 10)) :
              agent.metrics.cpu.value
          },
          memory: {
            ...agent.metrics.memory,
            value: agent.status === 'online' ? 
              Math.max(0, Math.min(100, agent.metrics.memory.value + (Math.random() - 0.5) * 5)) :
              agent.metrics.memory.value
          }
        }
      })));
    }, 5000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getStatusIcon = (status: AgentHealth['status']) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'offline':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'maintenance':
        return <Clock className="w-5 h-5 text-blue-500" />;
      default:
        return <Activity className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: AgentHealth['status']) => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'degraded':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'offline':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'maintenance':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getMetricIcon = (metricName: string) => {
    switch (metricName) {
      case 'CPU Usage':
        return <Cpu className="w-4 h-4" />;
      case 'Memory Usage':
        return <MemoryStick className="w-4 h-4" />;
      case 'Disk Usage':
        return <HardDrive className="w-4 h-4" />;
      case 'Network Latency':
        return <Wifi className="w-4 h-4" />;
      case 'Response Time':
        return <Clock className="w-4 h-4" />;
      case 'Throughput':
        return <Zap className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getMetricColor = (status: HealthMetric['status']) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'critical':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatUptime = (uptime: number) => {
    return `${uptime.toFixed(1)}%`;
  };

  const formatLastSeen = (date: Date) => {
    const diff = Date.now() - date.getTime();
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return `${Math.floor(diff / 86400000)}d ago`;
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {agents.map((agent) => (
          <div
            key={agent.id}
            onClick={() => setSelectedAgent(agent)}
            className={`p-4 border rounded-lg cursor-pointer transition-all ${
              selectedAgent?.id === agent.id
                ? 'border-red-500 bg-red-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900">{agent.name}</h4>
              {getStatusIcon(agent.status)}
            </div>
            <div className={`px-2 py-1 rounded text-xs font-medium mb-2 ${getStatusColor(agent.status)}`}>
              {agent.status}
            </div>
            <div className="space-y-1 text-sm text-gray-600">
              <div>Uptime: {formatUptime(agent.uptime)}</div>
              <div>Last seen: {formatLastSeen(agent.lastHeartbeat)}</div>
              <div>Version: {agent.version}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Detailed Health View */}
      {selectedAgent && (
        <div className="bg-white rounded-lg border">
          {/* Header */}
          <div className="p-6 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Heart className="w-6 h-6 text-red-600" />
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">{selectedAgent.name}</h3>
                  <p className="text-gray-600">Health Monitoring Dashboard</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <label className="flex items-center gap-2 text-sm text-gray-600">
                  <input
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                    className="rounded"
                  />
                  Auto-refresh
                </label>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedAgent.status)}`}>
                  {selectedAgent.status}
                </div>
              </div>
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="p-6">
            <h4 className="font-medium text-gray-900 mb-4">System Metrics</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.values(selectedAgent.metrics).map((metric) => (
                <div key={metric.name} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {getMetricIcon(metric.name)}
                      <span className="font-medium text-gray-900">{metric.name}</span>
                    </div>
                    <span className={`text-lg font-semibold ${getMetricColor(metric.status)}`}>
                      {metric.value}{metric.unit}
                    </span>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className={`h-2 rounded-full transition-all ${
                        metric.status === 'healthy' ? 'bg-green-500' :
                        metric.status === 'warning' ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}
                      style={{ 
                        width: metric.name === 'Throughput' ? 
                          `${Math.min(100, (metric.value / 100) * 100)}%` :
                          `${metric.value}%` 
                      }}
                    />
                  </div>
                  
                  {/* Thresholds */}
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Warning: {metric.threshold.warning}{metric.unit}</span>
                    <span>Critical: {metric.threshold.critical}{metric.unit}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Alerts */}
          {selectedAgent.alerts.length > 0 && (
            <div className="p-6 border-t">
              <h4 className="font-medium text-gray-900 mb-4">Recent Alerts</h4>
              <div className="space-y-3">
                {selectedAgent.alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`p-3 rounded-lg border-l-4 ${
                      alert.type === 'error' ? 'bg-red-50 border-red-500' :
                      alert.type === 'warning' ? 'bg-yellow-50 border-yellow-500' :
                      'bg-blue-50 border-blue-500'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900">{alert.message}</span>
                      <span className="text-sm text-gray-500">
                        {formatLastSeen(alert.timestamp)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
