"use client";

import React, { useState, useEffect } from 'react';
import { Shield, CheckCircle, XCircle, AlertTriangle, Clock, TrendingUp, TrendingDown, Target, Award, FileCheck } from 'lucide-react';

interface QualityGate {
  id: string;
  name: string;
  description: string;
  type: 'automated' | 'manual' | 'hybrid';
  status: 'passed' | 'failed' | 'pending' | 'skipped';
  criteria: Array<{
    name: string;
    required: boolean;
    status: 'passed' | 'failed' | 'pending';
    value?: number;
    threshold?: number;
    unit?: string;
  }>;
  lastRun: Date;
  duration: number;
  passRate: number;
}

interface QualityMetrics {
  overallScore: number;
  totalGates: number;
  passedGates: number;
  failedGates: number;
  pendingGates: number;
  averagePassRate: number;
  trend: 'up' | 'down' | 'stable';
  trendValue: number;
}

interface QualityReport {
  id: string;
  workflowId: string;
  workflowName: string;
  agentId: string;
  agentName: string;
  timestamp: Date;
  overallScore: number;
  gates: QualityGate[];
  recommendations: string[];
}

export default function QualityAssuranceDashboard() {
  const [metrics, setMetrics] = useState<QualityMetrics>({
    overallScore: 87,
    totalGates: 12,
    passedGates: 9,
    failedGates: 2,
    pendingGates: 1,
    averagePassRate: 85.3,
    trend: 'up',
    trendValue: 3.2
  });

  const [qualityGates, setQualityGates] = useState<QualityGate[]>([]);
  const [recentReports, setRecentReports] = useState<QualityReport[]>([]);
  const [selectedGate, setSelectedGate] = useState<QualityGate | null>(null);

  useEffect(() => {
    // Initialize with sample quality gates
    const sampleGates: QualityGate[] = [
      {
        id: 'gate-1',
        name: 'Code Quality Check',
        description: 'Automated code quality analysis including complexity, maintainability, and best practices',
        type: 'automated',
        status: 'passed',
        lastRun: new Date(Date.now() - 300000),
        duration: 45,
        passRate: 92,
        criteria: [
          { name: 'Cyclomatic Complexity', required: true, status: 'passed', value: 8, threshold: 10, unit: '' },
          { name: 'Code Coverage', required: true, status: 'passed', value: 85, threshold: 80, unit: '%' },
          { name: 'Maintainability Index', required: true, status: 'passed', value: 78, threshold: 70, unit: '' },
          { name: 'Technical Debt Ratio', required: false, status: 'passed', value: 3.2, threshold: 5, unit: '%' }
        ]
      },
      {
        id: 'gate-2',
        name: 'Security Validation',
        description: 'Security vulnerability scanning and compliance checks',
        type: 'automated',
        status: 'failed',
        lastRun: new Date(Date.now() - 600000),
        duration: 120,
        passRate: 67,
        criteria: [
          { name: 'Vulnerability Scan', required: true, status: 'failed', value: 3, threshold: 0, unit: 'issues' },
          { name: 'Dependency Check', required: true, status: 'passed', value: 0, threshold: 0, unit: 'vulnerabilities' },
          { name: 'OWASP Compliance', required: true, status: 'passed', value: 95, threshold: 90, unit: '%' },
          { name: 'Secret Detection', required: true, status: 'passed', value: 0, threshold: 0, unit: 'secrets' }
        ]
      },
      {
        id: 'gate-3',
        name: 'Performance Validation',
        description: 'Performance benchmarks and load testing validation',
        type: 'automated',
        status: 'passed',
        lastRun: new Date(Date.now() - 900000),
        duration: 180,
        passRate: 89,
        criteria: [
          { name: 'Response Time', required: true, status: 'passed', value: 120, threshold: 200, unit: 'ms' },
          { name: 'Throughput', required: true, status: 'passed', value: 850, threshold: 500, unit: 'req/min' },
          { name: 'Memory Usage', required: false, status: 'passed', value: 65, threshold: 80, unit: '%' },
          { name: 'CPU Utilization', required: false, status: 'passed', value: 45, threshold: 70, unit: '%' }
        ]
      },
      {
        id: 'gate-4',
        name: 'Content Quality Review',
        description: 'Manual review of generated content for accuracy and quality',
        type: 'manual',
        status: 'pending',
        lastRun: new Date(Date.now() - 1800000),
        duration: 0,
        passRate: 94,
        criteria: [
          { name: 'Accuracy Check', required: true, status: 'pending' },
          { name: 'Grammar & Style', required: true, status: 'pending' },
          { name: 'Tone Consistency', required: false, status: 'pending' },
          { name: 'Brand Compliance', required: true, status: 'pending' }
        ]
      },
      {
        id: 'gate-5',
        name: 'Integration Testing',
        description: 'End-to-end integration testing across all system components',
        type: 'automated',
        status: 'passed',
        lastRun: new Date(Date.now() - 1200000),
        duration: 300,
        passRate: 91,
        criteria: [
          { name: 'API Integration', required: true, status: 'passed', value: 100, threshold: 95, unit: '%' },
          { name: 'Database Connectivity', required: true, status: 'passed', value: 100, threshold: 100, unit: '%' },
          { name: 'External Services', required: true, status: 'passed', value: 98, threshold: 95, unit: '%' },
          { name: 'Error Handling', required: false, status: 'passed', value: 87, threshold: 80, unit: '%' }
        ]
      }
    ];

    const sampleReports: QualityReport[] = [
      {
        id: 'report-1',
        workflowId: 'workflow-1',
        workflowName: 'Content Generation Pipeline',
        agentId: 'writing-agent',
        agentName: 'Writing Agent',
        timestamp: new Date(Date.now() - 300000),
        overallScore: 89,
        gates: sampleGates.slice(0, 3),
        recommendations: [
          'Consider implementing additional security measures',
          'Optimize response time for better performance',
          'Add more comprehensive error handling'
        ]
      },
      {
        id: 'report-2',
        workflowId: 'workflow-2',
        workflowName: 'Code Review Workflow',
        agentId: 'code-agent',
        agentName: 'Code Agent',
        timestamp: new Date(Date.now() - 600000),
        overallScore: 94,
        gates: sampleGates.slice(1, 4),
        recommendations: [
          'Excellent code quality maintained',
          'Security practices are well implemented',
          'Consider automated performance monitoring'
        ]
      }
    ];

    setQualityGates(sampleGates);
    setRecentReports(sampleReports);
    setSelectedGate(sampleGates[0]);
  }, []);

  const getStatusIcon = (status: QualityGate['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'skipped':
        return <AlertTriangle className="w-5 h-5 text-gray-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: QualityGate['status']) => {
    switch (status) {
      case 'passed':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'skipped':
        return 'bg-gray-100 text-gray-700 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getCriteriaIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const formatDuration = (seconds: number) => {
    if (seconds === 0) return 'N/A';
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = () => {
    switch (metrics.trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Target className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Quality Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Overall Score</p>
              <p className={`text-2xl font-bold ${getScoreColor(metrics.overallScore)}`}>
                {metrics.overallScore}%
              </p>
            </div>
            <Award className="w-8 h-8 text-red-600" />
          </div>
          <div className="flex items-center gap-1 mt-2">
            {getTrendIcon()}
            <span className={`text-sm ${
              metrics.trend === 'up' ? 'text-green-600' : 
              metrics.trend === 'down' ? 'text-red-600' : 'text-gray-600'
            }`}>
              {metrics.trendValue}% from last week
            </span>
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Quality Gates</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.passedGates}/{metrics.totalGates}</p>
            </div>
            <Shield className="w-8 h-8 text-green-600" />
          </div>
          <p className="text-sm text-gray-600 mt-2">
            {((metrics.passedGates / metrics.totalGates) * 100).toFixed(1)}% passing
          </p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Average Pass Rate</p>
              <p className="text-2xl font-bold text-blue-600">{metrics.averagePassRate}%</p>
            </div>
            <Target className="w-8 h-8 text-blue-600" />
          </div>
          <p className="text-sm text-gray-600 mt-2">Across all gates</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Failed Gates</p>
              <p className="text-2xl font-bold text-red-600">{metrics.failedGates}</p>
            </div>
            <XCircle className="w-8 h-8 text-red-600" />
          </div>
          <p className="text-sm text-gray-600 mt-2">Require attention</p>
        </div>
      </div>

      {/* Quality Gates List */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="font-semibold text-gray-900 flex items-center gap-2">
            <FileCheck className="w-5 h-5 text-red-600" />
            Quality Gates
          </h3>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {qualityGates.map((gate) => (
              <div
                key={gate.id}
                onClick={() => setSelectedGate(gate)}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedGate?.id === gate.id
                    ? 'border-red-500 bg-red-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900">{gate.name}</h4>
                  {getStatusIcon(gate.status)}
                </div>
                
                <p className="text-sm text-gray-600 mb-3">{gate.description}</p>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(gate.status)}`}>
                      {gate.status}
                    </span>
                    <span className="text-gray-600">
                      {gate.type}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{gate.passRate}%</div>
                    <div className="text-gray-500">{formatDuration(gate.duration)}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Selected Gate Details */}
      {selectedGate && (
        <div className="bg-white rounded-lg border">
          <div className="p-6 border-b">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">{selectedGate.name}</h3>
                <p className="text-gray-600">{selectedGate.description}</p>
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedGate.status)}`}>
                {selectedGate.status}
              </div>
            </div>
          </div>

          <div className="p-6">
            <h4 className="font-medium text-gray-900 mb-4">Criteria Details</h4>
            <div className="space-y-3">
              {selectedGate.criteria.map((criterion, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    {getCriteriaIcon(criterion.status)}
                    <div>
                      <span className="font-medium text-gray-900">{criterion.name}</span>
                      {criterion.required && (
                        <span className="ml-2 px-2 py-0.5 bg-red-100 text-red-700 text-xs rounded">
                          Required
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    {criterion.value !== undefined && (
                      <div className="font-medium text-gray-900">
                        {criterion.value}{criterion.unit}
                        {criterion.threshold !== undefined && (
                          <span className="text-gray-500 ml-1">
                            / {criterion.threshold}{criterion.unit}
                          </span>
                        )}
                      </div>
                    )}
                    <div className={`text-sm ${
                      criterion.status === 'passed' ? 'text-green-600' :
                      criterion.status === 'failed' ? 'text-red-600' :
                      'text-yellow-600'
                    }`}>
                      {criterion.status}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-gray-900">{selectedGate.passRate}%</div>
                <div className="text-sm text-gray-600">Pass Rate</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-900">{formatDuration(selectedGate.duration)}</div>
                <div className="text-sm text-gray-600">Duration</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {selectedGate.lastRun.toLocaleTimeString()}
                </div>
                <div className="text-sm text-gray-600">Last Run</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Quality Reports */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="font-semibold text-gray-900">Recent Quality Reports</h3>
        </div>
        <div className="divide-y">
          {recentReports.map((report) => (
            <div key={report.id} className="p-6">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="font-medium text-gray-900">{report.workflowName}</h4>
                  <p className="text-sm text-gray-600">by {report.agentName}</p>
                </div>
                <div className="text-right">
                  <div className={`text-lg font-semibold ${getScoreColor(report.overallScore)}`}>
                    {report.overallScore}%
                  </div>
                  <div className="text-sm text-gray-500">
                    {report.timestamp.toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div className="mb-3">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-sm text-gray-600">Quality Gates:</span>
                  {report.gates.map((gate) => (
                    <span key={gate.id} className={`px-2 py-1 rounded text-xs ${getStatusColor(gate.status)}`}>
                      {gate.name}
                    </span>
                  ))}
                </div>
              </div>

              {report.recommendations.length > 0 && (
                <div>
                  <span className="text-sm text-gray-600 font-medium">Recommendations:</span>
                  <ul className="mt-1 text-sm text-gray-600 list-disc list-inside">
                    {report.recommendations.map((rec, index) => (
                      <li key={index}>{rec}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
