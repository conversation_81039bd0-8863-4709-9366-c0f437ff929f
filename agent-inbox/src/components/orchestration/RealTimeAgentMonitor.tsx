"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Activity, Wifi, WifiOff, AlertCircle, CheckCircle, Clock, Zap, Users, MessageSquare } from 'lucide-react';

interface AgentMessage {
  id: string;
  agentId: string;
  agentName: string;
  type: 'status' | 'task' | 'error' | 'heartbeat' | 'performance';
  message: string;
  timestamp: Date;
  data?: any;
}

interface RealTimeMetrics {
  messagesPerSecond: number;
  activeConnections: number;
  averageLatency: number;
  errorRate: number;
}

export default function RealTimeAgentMonitor() {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [metrics, setMetrics] = useState<RealTimeMetrics>({
    messagesPerSecond: 0,
    activeConnections: 0,
    averageLatency: 0,
    errorRate: 0
  });
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  
  const wsRef = useRef<WebSocket | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const metricsIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    connectWebSocket();
    startMetricsCollection();

    return () => {
      disconnectWebSocket();
      if (metricsIntervalRef.current) {
        clearInterval(metricsIntervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const connectWebSocket = () => {
    try {
      setConnectionStatus('connecting');
      
      // In a real implementation, this would connect to your WebSocket server
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/api/orchestration/ws`;
      
      // For demo purposes, we'll simulate WebSocket connection
      simulateWebSocketConnection();
      
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      setConnectionStatus('error');
      // Fallback to mock data
      simulateAgentMessages();
    }
  };

  const simulateWebSocketConnection = () => {
    // Simulate connection establishment
    setTimeout(() => {
      setConnectionStatus('connected');
      setIsConnected(true);
      simulateAgentMessages();
    }, 1000);
  };

  const simulateAgentMessages = () => {
    const agentNames = ['Research Agent', 'Writing Agent', 'Analysis Agent', 'Code Agent', 'QA Agent'];
    const messageTypes: AgentMessage['type'][] = ['status', 'task', 'error', 'heartbeat', 'performance'];
    
    const generateMessage = (): AgentMessage => {
      const agentName = agentNames[Math.floor(Math.random() * agentNames.length)];
      const type = messageTypes[Math.floor(Math.random() * messageTypes.length)];
      
      let message = '';
      let data = {};
      
      switch (type) {
        case 'status':
          message = `Agent ${agentName} status updated to ${['active', 'idle', 'busy'][Math.floor(Math.random() * 3)]}`;
          break;
        case 'task':
          message = `Task ${['started', 'completed', 'failed'][Math.floor(Math.random() * 3)]}: ${['Data analysis', 'Content generation', 'Code review', 'Quality check'][Math.floor(Math.random() * 4)]}`;
          break;
        case 'error':
          message = `Error: ${['Connection timeout', 'Invalid input', 'Resource unavailable', 'Rate limit exceeded'][Math.floor(Math.random() * 4)]}`;
          break;
        case 'heartbeat':
          message = `Heartbeat received - Agent healthy`;
          data = { cpu: Math.random() * 100, memory: Math.random() * 100 };
          break;
        case 'performance':
          message = `Performance metrics updated`;
          data = { 
            responseTime: Math.random() * 2000,
            throughput: Math.random() * 100,
            successRate: 85 + Math.random() * 15
          };
          break;
      }
      
      return {
        id: `msg-${Date.now()}-${Math.random()}`,
        agentId: `agent-${agentName.toLowerCase().replace(' ', '-')}`,
        agentName,
        type,
        message,
        timestamp: new Date(),
        data
      };
    };

    // Generate initial messages
    const initialMessages = Array.from({ length: 10 }, generateMessage);
    setMessages(initialMessages);

    // Continue generating messages
    const interval = setInterval(() => {
      const newMessage = generateMessage();
      setMessages(prev => [...prev.slice(-49), newMessage]); // Keep last 50 messages
    }, 2000 + Math.random() * 3000); // Random interval between 2-5 seconds

    return () => clearInterval(interval);
  };

  const startMetricsCollection = () => {
    metricsIntervalRef.current = setInterval(() => {
      setMetrics({
        messagesPerSecond: 2 + Math.random() * 3,
        activeConnections: 4 + Math.floor(Math.random() * 3),
        averageLatency: 50 + Math.random() * 100,
        errorRate: Math.random() * 5
      });
    }, 5000);
  };

  const disconnectWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    setIsConnected(false);
    setConnectionStatus('disconnected');
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getMessageIcon = (type: AgentMessage['type']) => {
    switch (type) {
      case 'status':
        return <Activity className="w-4 h-4 text-blue-500" />;
      case 'task':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'heartbeat':
        return <Zap className="w-4 h-4 text-yellow-500" />;
      case 'performance':
        return <Clock className="w-4 h-4 text-purple-500" />;
      default:
        return <MessageSquare className="w-4 h-4 text-gray-500" />;
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600';
      case 'connecting':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="w-4 h-4" />;
      case 'connecting':
        return <Activity className="w-4 h-4 animate-pulse" />;
      case 'error':
        return <WifiOff className="w-4 h-4" />;
      default:
        return <WifiOff className="w-4 h-4" />;
    }
  };

  return (
    <div className="bg-white rounded-lg border">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-900 flex items-center gap-2">
            <Users className="w-5 h-5 text-red-600" />
            Real-Time Agent Monitor
          </h3>
          <div className={`flex items-center gap-2 text-sm ${getConnectionStatusColor()}`}>
            {getConnectionStatusIcon()}
            <span className="capitalize">{connectionStatus}</span>
          </div>
        </div>
      </div>

      {/* Metrics */}
      <div className="p-4 border-b bg-gray-50">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{metrics.messagesPerSecond.toFixed(1)}</div>
            <div className="text-xs text-gray-600">Messages/sec</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{metrics.activeConnections}</div>
            <div className="text-xs text-gray-600">Active Agents</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{metrics.averageLatency.toFixed(0)}ms</div>
            <div className="text-xs text-gray-600">Avg Latency</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{metrics.errorRate.toFixed(1)}%</div>
            <div className="text-xs text-gray-600">Error Rate</div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="h-96 overflow-y-auto p-4">
        <div className="space-y-3">
          {messages.map((message) => (
            <div key={message.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex-shrink-0 mt-0.5">
                {getMessageIcon(message.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-sm text-gray-900">{message.agentName}</span>
                  <span className={`px-2 py-0.5 rounded text-xs ${
                    message.type === 'error' ? 'bg-red-100 text-red-700' :
                    message.type === 'task' ? 'bg-green-100 text-green-700' :
                    message.type === 'performance' ? 'bg-purple-100 text-purple-700' :
                    'bg-blue-100 text-blue-700'
                  }`}>
                    {message.type}
                  </span>
                  <span className="text-xs text-gray-500">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <p className="text-sm text-gray-700">{message.message}</p>
                {message.data && Object.keys(message.data).length > 0 && (
                  <div className="mt-2 text-xs text-gray-600 font-mono bg-white p-2 rounded border">
                    {JSON.stringify(message.data, null, 2)}
                  </div>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Showing last {messages.length} messages</span>
          <button
            onClick={() => setMessages([])}
            className="text-red-600 hover:text-red-700 font-medium"
          >
            Clear Messages
          </button>
        </div>
      </div>
    </div>
  );
}
