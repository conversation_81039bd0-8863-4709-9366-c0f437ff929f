"use client";

import React, { useState, useEffect } from 'react';
import { GitBranch, Play, Pause, Square, RotateCcw, Clock, Users, Target, AlertTriangle, CheckCircle, ArrowRight } from 'lucide-react';

interface TaskNode {
  id: string;
  name: string;
  description: string;
  agentId?: string;
  agentName?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'blocked';
  dependencies: string[];
  estimatedDuration: number;
  actualDuration?: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  progress: number;
  startTime?: Date;
  endTime?: Date;
  errorMessage?: string;
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'paused' | 'completed' | 'failed';
  tasks: TaskNode[];
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  totalProgress: number;
}

export default function AdvancedTaskOrchestration() {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [showCreateWorkflow, setShowCreateWorkflow] = useState(false);

  useEffect(() => {
    // Initialize with sample workflows
    const sampleWorkflows: Workflow[] = [
      {
        id: 'workflow-1',
        name: 'Content Generation Pipeline',
        description: 'Multi-stage content creation with research, writing, and review',
        status: 'running',
        createdAt: new Date(Date.now() - 86400000),
        startedAt: new Date(Date.now() - 3600000),
        totalProgress: 65,
        tasks: [
          {
            id: 'task-1',
            name: 'Research Phase',
            description: 'Gather information and sources',
            agentId: 'research-agent',
            agentName: 'Research Agent',
            status: 'completed',
            dependencies: [],
            estimatedDuration: 30,
            actualDuration: 28,
            priority: 'high',
            progress: 100,
            startTime: new Date(Date.now() - 3600000),
            endTime: new Date(Date.now() - 1800000)
          },
          {
            id: 'task-2',
            name: 'Content Writing',
            description: 'Create initial content draft',
            agentId: 'writing-agent',
            agentName: 'Writing Agent',
            status: 'running',
            dependencies: ['task-1'],
            estimatedDuration: 45,
            priority: 'high',
            progress: 75,
            startTime: new Date(Date.now() - 1800000)
          },
          {
            id: 'task-3',
            name: 'Quality Review',
            description: 'Review and validate content quality',
            agentId: 'qa-agent',
            agentName: 'QA Agent',
            status: 'pending',
            dependencies: ['task-2'],
            estimatedDuration: 20,
            priority: 'medium',
            progress: 0
          },
          {
            id: 'task-4',
            name: 'Final Editing',
            description: 'Polish and finalize content',
            agentId: 'editor-agent',
            agentName: 'Editor Agent',
            status: 'pending',
            dependencies: ['task-3'],
            estimatedDuration: 15,
            priority: 'medium',
            progress: 0
          }
        ]
      },
      {
        id: 'workflow-2',
        name: 'Code Review Workflow',
        description: 'Automated code analysis and review process',
        status: 'completed',
        createdAt: new Date(Date.now() - 172800000),
        startedAt: new Date(Date.now() - 86400000),
        completedAt: new Date(Date.now() - 3600000),
        totalProgress: 100,
        tasks: [
          {
            id: 'task-5',
            name: 'Static Analysis',
            description: 'Run static code analysis tools',
            agentId: 'analysis-agent',
            agentName: 'Analysis Agent',
            status: 'completed',
            dependencies: [],
            estimatedDuration: 10,
            actualDuration: 8,
            priority: 'high',
            progress: 100,
            startTime: new Date(Date.now() - 86400000),
            endTime: new Date(Date.now() - 86000000)
          },
          {
            id: 'task-6',
            name: 'Security Scan',
            description: 'Check for security vulnerabilities',
            agentId: 'security-agent',
            agentName: 'Security Agent',
            status: 'completed',
            dependencies: [],
            estimatedDuration: 15,
            actualDuration: 12,
            priority: 'critical',
            progress: 100,
            startTime: new Date(Date.now() - 86400000),
            endTime: new Date(Date.now() - 85000000)
          },
          {
            id: 'task-7',
            name: 'Performance Review',
            description: 'Analyze performance implications',
            agentId: 'perf-agent',
            agentName: 'Performance Agent',
            status: 'completed',
            dependencies: ['task-5'],
            estimatedDuration: 20,
            actualDuration: 18,
            priority: 'medium',
            progress: 100,
            startTime: new Date(Date.now() - 85000000),
            endTime: new Date(Date.now() - 84000000)
          }
        ]
      }
    ];

    setWorkflows(sampleWorkflows);
    setSelectedWorkflow(sampleWorkflows[0]);
  }, []);

  const getStatusIcon = (status: TaskNode['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'running':
        return <Play className="w-4 h-4 text-blue-500 animate-pulse" />;
      case 'failed':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'blocked':
        return <Square className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TaskNode['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'running':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'failed':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'blocked':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getPriorityColor = (priority: TaskNode['priority']) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const canTaskStart = (task: TaskNode, allTasks: TaskNode[]) => {
    return task.dependencies.every(depId => {
      const depTask = allTasks.find(t => t.id === depId);
      return depTask?.status === 'completed';
    });
  };

  const handleWorkflowAction = (action: 'start' | 'pause' | 'stop' | 'restart') => {
    if (!selectedWorkflow) return;

    console.log(`${action} workflow:`, selectedWorkflow.name);
    // In a real implementation, this would call the orchestration API
  };

  const handleTaskAction = (taskId: string, action: 'start' | 'pause' | 'retry') => {
    console.log(`${action} task:`, taskId);
    // In a real implementation, this would call the task management API
  };

  return (
    <div className="space-y-6">
      {/* Workflow Selection */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-gray-900 flex items-center gap-2">
            <GitBranch className="w-5 h-5 text-red-600" />
            Active Workflows
          </h3>
          <button
            onClick={() => setShowCreateWorkflow(true)}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Create Workflow
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {workflows.map((workflow) => (
            <div
              key={workflow.id}
              onClick={() => setSelectedWorkflow(workflow)}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedWorkflow?.id === workflow.id
                  ? 'border-red-500 bg-red-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{workflow.name}</h4>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  workflow.status === 'completed' ? 'bg-green-100 text-green-700' :
                  workflow.status === 'running' ? 'bg-blue-100 text-blue-700' :
                  workflow.status === 'failed' ? 'bg-red-100 text-red-700' :
                  'bg-gray-100 text-gray-700'
                }`}>
                  {workflow.status}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-3">{workflow.description}</p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">{workflow.tasks.length} tasks</span>
                <div className="flex items-center gap-2">
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-red-600 h-2 rounded-full transition-all"
                      style={{ width: `${workflow.totalProgress}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600">{workflow.totalProgress}%</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Selected Workflow Details */}
      {selectedWorkflow && (
        <div className="bg-white rounded-lg border">
          {/* Workflow Header */}
          <div className="p-6 border-b">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">{selectedWorkflow.name}</h3>
                <p className="text-gray-600">{selectedWorkflow.description}</p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleWorkflowAction('start')}
                  className="p-2 text-green-600 hover:bg-green-100 rounded-lg"
                  title="Start Workflow"
                >
                  <Play className="w-5 h-5" />
                </button>
                <button
                  onClick={() => handleWorkflowAction('pause')}
                  className="p-2 text-yellow-600 hover:bg-yellow-100 rounded-lg"
                  title="Pause Workflow"
                >
                  <Pause className="w-5 h-5" />
                </button>
                <button
                  onClick={() => handleWorkflowAction('stop')}
                  className="p-2 text-red-600 hover:bg-red-100 rounded-lg"
                  title="Stop Workflow"
                >
                  <Square className="w-5 h-5" />
                </button>
                <button
                  onClick={() => handleWorkflowAction('restart')}
                  className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg"
                  title="Restart Workflow"
                >
                  <RotateCcw className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Workflow Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">{selectedWorkflow.tasks.length}</div>
                <div className="text-sm text-gray-600">Total Tasks</div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-green-600">
                  {selectedWorkflow.tasks.filter(t => t.status === 'completed').length}
                </div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-blue-600">
                  {selectedWorkflow.tasks.filter(t => t.status === 'running').length}
                </div>
                <div className="text-sm text-gray-600">Running</div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">{selectedWorkflow.totalProgress}%</div>
                <div className="text-sm text-gray-600">Progress</div>
              </div>
            </div>
          </div>

          {/* Task List */}
          <div className="p-6">
            <h4 className="font-medium text-gray-900 mb-4">Task Pipeline</h4>
            <div className="space-y-4">
              {selectedWorkflow.tasks.map((task, index) => (
                <div key={task.id} className="relative">
                  {/* Connection Line */}
                  {index < selectedWorkflow.tasks.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-200" />
                  )}
                  
                  <div className={`border rounded-lg p-4 ${getStatusColor(task.status)}`}>
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(task.status)}
                        <div>
                          <h5 className="font-medium text-gray-900">{task.name}</h5>
                          <p className="text-sm text-gray-600">{task.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${getPriorityColor(task.priority)}`} />
                        <span className="text-sm text-gray-600 capitalize">{task.priority}</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Agent:</span>
                        <span className="ml-1 font-medium">{task.agentName || 'Unassigned'}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Duration:</span>
                        <span className="ml-1 font-medium">
                          {task.actualDuration ? formatDuration(task.actualDuration) : formatDuration(task.estimatedDuration)}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">Progress:</span>
                        <span className="ml-1 font-medium">{task.progress}%</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Dependencies:</span>
                        <span className="ml-1 font-medium">{task.dependencies.length}</span>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mt-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all ${
                            task.status === 'completed' ? 'bg-green-500' :
                            task.status === 'running' ? 'bg-blue-500' :
                            task.status === 'failed' ? 'bg-red-500' :
                            'bg-gray-400'
                          }`}
                          style={{ width: `${task.progress}%` }}
                        />
                      </div>
                    </div>

                    {/* Task Actions */}
                    {task.status !== 'completed' && (
                      <div className="mt-3 flex items-center gap-2">
                        {task.status === 'pending' && canTaskStart(task, selectedWorkflow.tasks) && (
                          <button
                            onClick={() => handleTaskAction(task.id, 'start')}
                            className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                          >
                            Start
                          </button>
                        )}
                        {task.status === 'running' && (
                          <button
                            onClick={() => handleTaskAction(task.id, 'pause')}
                            className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700"
                          >
                            Pause
                          </button>
                        )}
                        {task.status === 'failed' && (
                          <button
                            onClick={() => handleTaskAction(task.id, 'retry')}
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                          >
                            Retry
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
