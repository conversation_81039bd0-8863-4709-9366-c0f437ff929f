'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { TerminalSession, TerminalConfiguration, TerminalTheme } from './types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Copy, 
  Search, 
  MoreVertical,
  X,
  Maximize2,
  Minimize2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TerminalProps {
  session?: TerminalSession;
  configuration: TerminalConfiguration;
  onCommand?: (command: string) => void;
  onResize?: (size: { cols: number; rows: number }) => void;
  onSessionChange?: (session: TerminalSession) => void;
  onClose?: () => void;
  className?: string;
}

export const WebTerminal: React.FC<TerminalProps> = ({
  session,
  configuration,
  onCommand,
  onResize,
  onSessionChange,
  onClose,
  className = ''
}) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<Terminal>();
  const fitAddonRef = useRef<FitAddon>();
  const webLinksAddonRef = useRef<WebLinksAddon>();
  
  const [isConnected, setIsConnected] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);

  useEffect(() => {
    if (!terminalRef.current) return;

    // Initialize XTerm.js terminal
    const terminal = new Terminal({
      theme: convertTheme(configuration.theme),
      fontSize: configuration.fontSize,
      fontFamily: configuration.fontFamily,
      scrollback: configuration.scrollback,
      bellStyle: configuration.bellStyle,
      cursorBlink: configuration.cursorBlink,
      cursorStyle: configuration.cursorStyle,
      allowTransparency: configuration.allowTransparency,
      convertEol: true,
      disableStdin: false,
      cols: 80,
      rows: 24
    });

    // Add addons
    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();

    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);

    // Open terminal in container
    terminal.open(terminalRef.current);

    // Store references
    xtermRef.current = terminal;
    fitAddonRef.current = fitAddon;
    webLinksAddonRef.current = webLinksAddon;

    // Setup event handlers
    setupTerminalEvents(terminal);

    // Fit terminal to container
    setTimeout(() => fitAddon.fit(), 100);

    // Setup mock terminal
    setupMockTerminal();

    // Setup resize observer
    const resizeObserver = new ResizeObserver(() => {
      setTimeout(() => fitAddon.fit(), 100);
    });
    resizeObserver.observe(terminalRef.current);

    return () => {
      terminal.dispose();
      resizeObserver.disconnect();
    };
  }, []);

  const setupTerminalEvents = (terminal: Terminal) => {
    terminal.onResize(({ cols, rows }) => {
      onResize?.({ cols, rows });
    });
  };

  const setupMockTerminal = () => {
    // Mock terminal for demonstration
    const terminal = xtermRef.current;
    if (!terminal) return;

    terminal.writeln('\x1b[32m✓\x1b[0m Welcome to PIB Agent Development Environment');
    terminal.writeln('\x1b[36mTerminal Integration Active\x1b[0m');
    terminal.writeln('');
    terminal.writeln('Available commands:');
    terminal.writeln('  \x1b[33mhelp\x1b[0m     - Show available commands');
    terminal.writeln('  \x1b[33mclear\x1b[0m    - Clear the terminal');
    terminal.writeln('  \x1b[33mdate\x1b[0m     - Show current date');
    terminal.writeln('  \x1b[33mecho\x1b[0m     - Echo text back');
    terminal.writeln('  \x1b[33mls\x1b[0m       - List directory contents');
    terminal.writeln('  \x1b[33mpwd\x1b[0m      - Show current directory');
    terminal.writeln('');
    terminal.write('\x1b[32m$\x1b[0m ');

    let currentCommand = '';
    terminal.onData((data) => {
      if (data === '\r') {
        terminal.writeln('');
        if (currentCommand.trim()) {
          handleMockCommand(currentCommand.trim());
          onCommand?.(currentCommand.trim());
        }
        currentCommand = '';
        terminal.write('\x1b[32m$\x1b[0m ');
      } else if (data === '\u007f') { // Backspace
        if (currentCommand.length > 0) {
          currentCommand = currentCommand.slice(0, -1);
          terminal.write('\b \b');
        }
      } else if (data === '\u0003') { // Ctrl+C
        terminal.writeln('^C');
        currentCommand = '';
        terminal.write('\x1b[32m$\x1b[0m ');
      } else {
        currentCommand += data;
        terminal.write(data);
      }
    });

    setIsConnected(true);
  };

  const handleMockCommand = (command: string) => {
    const terminal = xtermRef.current;
    if (!terminal) return;

    const args = command.split(' ');
    const cmd = args[0];

    // Simulate command execution delay for realism
    const simulateDelay = (callback: () => void, delay = 100) => {
      setTimeout(callback, delay);
    };

    switch (cmd) {
      case 'help':
        terminal.writeln('PIB Agent Development Environment Commands:');
        terminal.writeln('');
        terminal.writeln('\x1b[36mBasic Commands:\x1b[0m');
        terminal.writeln('  \x1b[33mhelp\x1b[0m     - Show this help message');
        terminal.writeln('  \x1b[33mclear\x1b[0m    - Clear the terminal');
        terminal.writeln('  \x1b[33mdate\x1b[0m     - Show current date and time');
        terminal.writeln('  \x1b[33mecho\x1b[0m     - Echo text back');
        terminal.writeln('  \x1b[33mls\x1b[0m       - List directory contents');
        terminal.writeln('  \x1b[33mpwd\x1b[0m      - Show current working directory');
        terminal.writeln('  \x1b[33mwhoami\x1b[0m   - Show current user');
        terminal.writeln('');
        terminal.writeln('\x1b[36mDevelopment Commands:\x1b[0m');
        terminal.writeln('  \x1b[33mnode -v\x1b[0m  - Show Node.js version');
        terminal.writeln('  \x1b[33mnpm -v\x1b[0m   - Show npm version');
        terminal.writeln('  \x1b[33mpnpm -v\x1b[0m  - Show pnpm version');
        terminal.writeln('  \x1b[33mgit status\x1b[0m - Show git repository status');
        terminal.writeln('  \x1b[33mgit log\x1b[0m  - Show recent commits');
        terminal.writeln('  \x1b[33mps\x1b[0m       - Show running processes');
        terminal.writeln('  \x1b[33mtop\x1b[0m      - Show system resources');
        terminal.writeln('');
        terminal.writeln('\x1b[36mProject Commands:\x1b[0m');
        terminal.writeln('  \x1b[33mnpm install\x1b[0m - Install dependencies');
        terminal.writeln('  \x1b[33mnpm run dev\x1b[0m - Start development server');
        terminal.writeln('  \x1b[33mnpm run build\x1b[0m - Build for production');
        terminal.writeln('  \x1b[33mnpm test\x1b[0m - Run tests');
        break;
      case 'clear':
        terminal.clear();
        terminal.writeln('\x1b[32m✓\x1b[0m Terminal cleared');
        break;
      case 'date':
        terminal.writeln(new Date().toString());
        break;
      case 'ls':
        if (args[1] === '-la' || args[1] === '-l') {
          terminal.writeln('total 24');
          terminal.writeln('drwxr-xr-x  8 <USER>  <GROUP>   256 Dec 17 10:30 \x1b[34magent-inbox/\x1b[0m');
          terminal.writeln('drwxr-xr-x  4 <USER>  <GROUP>   128 Dec 17 10:30 \x1b[34mapps/\x1b[0m');
          terminal.writeln('drwxr-xr-x  6 <USER>  <GROUP>   192 Dec 17 10:30 \x1b[34mpackages/\x1b[0m');
          terminal.writeln('drwxr-xr-x 12 <USER>  <GROUP>   384 Dec 17 10:30 \x1b[34msrc/\x1b[0m');
          terminal.writeln('drwxr-xr-x  8 <USER>  <GROUP>   256 Dec 17 10:30 \x1b[34mcomponents/\x1b[0m');
          terminal.writeln('drwxr-xr-x  4 <USER>  <GROUP>   128 Dec 17 10:30 \x1b[34mlib/\x1b[0m');
          terminal.writeln('-rw-r--r--  1 <USER>  <GROUP>  2048 Dec 17 10:30 README.md');
          terminal.writeln('-rw-r--r--  1 <USER>  <GROUP>  1536 Dec 17 10:30 package.json');
          terminal.writeln('-rw-r--r--  1 <USER>  <GROUP>   768 Dec 17 10:30 tsconfig.json');
        } else {
          terminal.writeln('\x1b[34magent-inbox/\x1b[0m     \x1b[34mapps/\x1b[0m           \x1b[34mpackages/\x1b[0m');
          terminal.writeln('\x1b[34msrc/\x1b[0m            \x1b[34mcomponents/\x1b[0m     \x1b[34mlib/\x1b[0m');
          terminal.writeln('README.md       package.json    tsconfig.json');
        }
        break;
      case 'pwd':
        terminal.writeln('/workspace/pib-agent');
        break;
      case 'whoami':
        terminal.writeln('pib-developer');
        break;
      case 'node':
        if (args[1] === '-v') {
          terminal.writeln('v18.17.0');
        } else {
          terminal.writeln('Node.js REPL not available in demo mode');
          terminal.writeln('Use \x1b[33mnode -v\x1b[0m to check version');
        }
        break;
      case 'npm':
        if (args[1] === '-v') {
          terminal.writeln('9.6.7');
        } else if (args[1] === 'install') {
          terminal.writeln('\x1b[33m⚠\x1b[0m  Running npm install...');
          simulateDelay(() => {
            terminal.writeln('npm WARN deprecated package@1.0.0: Package deprecated');
            terminal.writeln('added 1247 packages, and audited 1248 packages in 3s');
            terminal.writeln('\x1b[32m✓\x1b[0m Dependencies installed successfully');
          }, 1500);
          return;
        } else if (args[1] === 'run') {
          if (args[2] === 'dev') {
            terminal.writeln('\x1b[33m⚠\x1b[0m  Starting development server...');
            simulateDelay(() => {
              terminal.writeln('> pib-agent@1.0.0 dev');
              terminal.writeln('> next dev');
              terminal.writeln('');
              terminal.writeln('\x1b[32m✓\x1b[0m Ready on http://localhost:3001');
              terminal.writeln('\x1b[32m✓\x1b[0m Development server running');
            }, 2000);
            return;
          } else if (args[2] === 'build') {
            terminal.writeln('\x1b[33m⚠\x1b[0m  Building for production...');
            simulateDelay(() => {
              terminal.writeln('> pib-agent@1.0.0 build');
              terminal.writeln('> next build');
              terminal.writeln('');
              terminal.writeln('Creating an optimized production build...');
              terminal.writeln('\x1b[32m✓\x1b[0m Compiled successfully');
              terminal.writeln('\x1b[32m✓\x1b[0m Build completed in 45.2s');
            }, 3000);
            return;
          }
        } else if (args[1] === 'test') {
          terminal.writeln('\x1b[33m⚠\x1b[0m  Running tests...');
          simulateDelay(() => {
            terminal.writeln('> pib-agent@1.0.0 test');
            terminal.writeln('> jest');
            terminal.writeln('');
            terminal.writeln('PASS  src/components/Button.test.tsx');
            terminal.writeln('PASS  src/components/Input.test.tsx');
            terminal.writeln('PASS  src/utils/helpers.test.ts');
            terminal.writeln('');
            terminal.writeln('Test Suites: 3 passed, 3 total');
            terminal.writeln('Tests:       12 passed, 12 total');
            terminal.writeln('\x1b[32m✓\x1b[0m All tests passed');
          }, 2500);
          return;
        } else {
          terminal.writeln('npm command not fully available in demo mode');
          terminal.writeln('Available: \x1b[33mnpm -v\x1b[0m, \x1b[33mnpm install\x1b[0m, \x1b[33mnpm run dev\x1b[0m, \x1b[33mnpm run build\x1b[0m, \x1b[33mnpm test\x1b[0m');
        }
        break;
      case 'pnpm':
        if (args[1] === '-v') {
          terminal.writeln('8.10.5');
        } else {
          terminal.writeln('pnpm command not available in demo mode');
          terminal.writeln('Use \x1b[33mpnpm -v\x1b[0m to check version');
        }
        break;
      case 'git':
        if (args[1] === 'status') {
          terminal.writeln('On branch main');
          terminal.writeln('Your branch is up to date with \'origin/main\'.');
          terminal.writeln('');
          terminal.writeln('Changes not staged for commit:');
          terminal.writeln('  (use "git add <file>..." to update what will be committed)');
          terminal.writeln('  (use "git checkout -- <file>..." to discard changes in working directory)');
          terminal.writeln('');
          terminal.writeln('\x1b[31m\tmodified:   src/components/terminal/web-terminal.tsx\x1b[0m');
          terminal.writeln('\x1b[31m\tmodified:   src/app/code/page.tsx\x1b[0m');
          terminal.writeln('');
          terminal.writeln('Untracked files:');
          terminal.writeln('  (use "git add <file>..." to include in what will be committed)');
          terminal.writeln('');
          terminal.writeln('\x1b[31m\tsrc/components/terminal/types.ts\x1b[0m');
          terminal.writeln('');
          terminal.writeln('no changes added to commit (use "git add" or "git commit -a")');
        } else if (args[1] === 'log') {
          terminal.writeln('\x1b[33mcommit a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8 (HEAD -> main, origin/main)\x1b[0m');
          terminal.writeln('Author: PIB Developer <<EMAIL>>');
          terminal.writeln('Date:   Mon Dec 17 10:30:00 2024 +0000');
          terminal.writeln('');
          terminal.writeln('    feat: Enhanced terminal with realistic command execution');
          terminal.writeln('');
          terminal.writeln('\x1b[33mcommit b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9\x1b[0m');
          terminal.writeln('Author: PIB Developer <<EMAIL>>');
          terminal.writeln('Date:   Mon Dec 17 09:15:00 2024 +0000');
          terminal.writeln('');
          terminal.writeln('    feat: Integrated Monaco Editor with Development Environment');
          terminal.writeln('');
          terminal.writeln('\x1b[33mcommit c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0\x1b[0m');
          terminal.writeln('Author: PIB Developer <<EMAIL>>');
          terminal.writeln('Date:   Sun Dec 16 16:45:00 2024 +0000');
          terminal.writeln('');
          terminal.writeln('    feat: Added Visual Workflows MCP server integration');
        } else {
          terminal.writeln('git command not fully available in demo mode');
          terminal.writeln('Available: \x1b[33mgit status\x1b[0m, \x1b[33mgit log\x1b[0m');
        }
        break;
      case 'ps':
        terminal.writeln('  PID TTY           TIME CMD');
        terminal.writeln(' 1234 ttys000    0:00.12 -bash');
        terminal.writeln(' 5678 ttys000    0:02.45 node /workspace/pib-agent/node_modules/.bin/next dev');
        terminal.writeln(' 9012 ttys000    0:00.08 /usr/bin/ssh-agent -l');
        terminal.writeln(' 3456 ttys001    0:00.03 /bin/bash');
        break;
      case 'top':
        terminal.writeln('Processes: 156 total, 2 running, 154 sleeping, 892 threads');
        terminal.writeln('Load Avg: 1.23, 1.45, 1.67  CPU usage: 12.3% user, 4.5% sys, 83.2% idle');
        terminal.writeln('SharedLibs: 245M resident, 67M data, 23M linkedit.');
        terminal.writeln('MemRegions: 45678 total, 2.1G resident, 156M private, 890M shared.');
        terminal.writeln('PhysMem: 16G used (2.3G wired), 0B unused.');
        terminal.writeln('VM: 1.2T vsize, 1.1G framework vsize, 0(0) swapins, 0(0) swapouts.');
        terminal.writeln('Networks: packets: 12345/1.2M in, 9876/890K out.');
        terminal.writeln('Disks: 234567/2.1G read, 123456/1.1G written.');
        terminal.writeln('');
        terminal.writeln('PID    COMMAND      %CPU TIME     #TH   #WQ  #PORT MEM    PURG   CMPRS');
        terminal.writeln('1234   node         8.2  0:02.45  12    0    45    156M   0B     0B');
        terminal.writeln('5678   Terminal     2.1  0:00.12  8     1    23    89M    0B     0B');
        break;
      case 'cat':
        if (args[1]) {
          if (args[1] === 'package.json') {
            terminal.writeln('{');
            terminal.writeln('  "name": "pib-agent",');
            terminal.writeln('  "version": "1.0.0",');
            terminal.writeln('  "description": "Unified AI Assistant Platform",');
            terminal.writeln('  "main": "index.js",');
            terminal.writeln('  "scripts": {');
            terminal.writeln('    "dev": "next dev",');
            terminal.writeln('    "build": "next build",');
            terminal.writeln('    "start": "next start",');
            terminal.writeln('    "test": "jest"');
            terminal.writeln('  },');
            terminal.writeln('  "dependencies": {');
            terminal.writeln('    "next": "^14.0.0",');
            terminal.writeln('    "react": "^18.0.0",');
            terminal.writeln('    "typescript": "^5.0.0"');
            terminal.writeln('  }');
            terminal.writeln('}');
          } else if (args[1] === 'README.md') {
            terminal.writeln('# PIB Agent - Unified AI Assistant Platform');
            terminal.writeln('');
            terminal.writeln('A comprehensive AI assistant platform with multiple specialized modules:');
            terminal.writeln('');
            terminal.writeln('## Features');
            terminal.writeln('- 🎨 Visual Design Studio');
            terminal.writeln('- 💻 Development Environment');
            terminal.writeln('- 🤖 Agent Orchestration');
            terminal.writeln('- 🔄 Visual Workflows');
            terminal.writeln('- 📱 Social Media Hub');
            terminal.writeln('- ⚡ Automation Engine');
            terminal.writeln('');
            terminal.writeln('## Getting Started');
            terminal.writeln('```bash');
            terminal.writeln('npm install');
            terminal.writeln('npm run dev');
            terminal.writeln('```');
          } else {
            terminal.writeln(`cat: ${args[1]}: No such file or directory`);
          }
        } else {
          terminal.writeln('cat: missing file operand');
          terminal.writeln('Try \'cat --help\' for more information.');
        }
        break;
      case 'mkdir':
        if (args[1]) {
          terminal.writeln(`\x1b[32m✓\x1b[0m Directory '${args[1]}' created`);
        } else {
          terminal.writeln('mkdir: missing operand');
          terminal.writeln('Try \'mkdir --help\' for more information.');
        }
        break;
      case 'touch':
        if (args[1]) {
          terminal.writeln(`\x1b[32m✓\x1b[0m File '${args[1]}' created`);
        } else {
          terminal.writeln('touch: missing file operand');
        }
        break;
      case 'history':
        terminal.writeln('    1  ls -la');
        terminal.writeln('    2  git status');
        terminal.writeln('    3  npm install');
        terminal.writeln('    4  npm run dev');
        terminal.writeln('    5  git log');
        terminal.writeln('    6  ps');
        terminal.writeln('    7  top');
        terminal.writeln('    8  help');
        terminal.writeln('    9  history');
        break;
      default:
        if (command.startsWith('echo ')) {
          const message = command.substring(5);
          terminal.writeln(message);
        } else if (command.trim() === '') {
          // Do nothing for empty commands
        } else {
          terminal.writeln(`\x1b[31mCommand not found: ${command}\x1b[0m`);
          terminal.writeln('Type \x1b[33mhelp\x1b[0m for available commands');
        }
    }
  };

  const convertTheme = (theme: TerminalTheme) => {
    return {
      background: theme.background,
      foreground: theme.foreground,
      cursor: theme.cursor,
      selection: theme.selection,
      black: theme.black,
      red: theme.red,
      green: theme.green,
      yellow: theme.yellow,
      blue: theme.blue,
      magenta: theme.magenta,
      cyan: theme.cyan,
      white: theme.white,
      brightBlack: theme.brightBlack,
      brightRed: theme.brightRed,
      brightGreen: theme.brightGreen,
      brightYellow: theme.brightYellow,
      brightBlue: theme.brightBlue,
      brightMagenta: theme.brightMagenta,
      brightCyan: theme.brightCyan,
      brightWhite: theme.brightWhite,
    };
  };

  const handleCopyToClipboard = () => {
    const terminal = xtermRef.current;
    if (!terminal) return;

    const selection = terminal.getSelection();
    if (selection) {
      navigator.clipboard.writeText(selection);
    }
  };

  const handleClearTerminal = () => {
    const terminal = xtermRef.current;
    if (terminal) {
      terminal.clear();
      terminal.writeln('\x1b[32m✓\x1b[0m Terminal cleared');
      terminal.write('\x1b[32m$\x1b[0m ');
    }
  };

  const handleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  return (
    <div className={`web-terminal ${className} ${isMaximized ? 'fixed inset-0 z-50' : ''}`}>
      <div className="terminal-header flex items-center justify-between bg-gray-800 text-white px-4 py-2">
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm font-medium">
            {session?.name || 'Development Terminal'}
          </span>
          {session && (
            <Badge variant="secondary" className="text-xs">
              {session.shell}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopyToClipboard}
            className="text-white hover:bg-gray-700"
          >
            <Copy size={16} />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleMaximize}
            className="text-white hover:bg-gray-700"
          >
            {isMaximized ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="text-white hover:bg-gray-700">
                <MoreVertical size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleClearTerminal}>
                Clear Terminal
              </DropdownMenuItem>
              <DropdownMenuItem>
                Split Terminal
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-gray-700"
            >
              <X size={16} />
            </Button>
          )}
        </div>
      </div>
      
      <div 
        ref={terminalRef} 
        className="terminal-container flex-1"
        style={{ 
          backgroundColor: configuration.theme.background,
          height: isMaximized ? 'calc(100vh - 40px)' : '100%'
        }}
      />
      
      {session && (
        <div className="terminal-status-bar bg-gray-700 text-white px-4 py-1 text-xs flex justify-between">
          <span>
            {session.cwd}
          </span>
          <span>
            {session.shell} | {session.history.length} commands
          </span>
        </div>
      )}
    </div>
  );
};

export default WebTerminal;
