'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { TerminalSession, TerminalConfiguration, TerminalTheme } from './types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Copy, 
  Search, 
  MoreVertical,
  X,
  Maximize2,
  Minimize2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TerminalProps {
  session?: TerminalSession;
  configuration: TerminalConfiguration;
  onCommand?: (command: string) => void;
  onResize?: (size: { cols: number; rows: number }) => void;
  onSessionChange?: (session: TerminalSession) => void;
  onClose?: () => void;
  className?: string;
}

export const WebTerminal: React.FC<TerminalProps> = ({
  session,
  configuration,
  onCommand,
  onResize,
  onSessionChange,
  onClose,
  className = ''
}) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<Terminal>();
  const fitAddonRef = useRef<FitAddon>();
  const webLinksAddonRef = useRef<WebLinksAddon>();
  
  const [isConnected, setIsConnected] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);

  useEffect(() => {
    if (!terminalRef.current) return;

    // Initialize XTerm.js terminal
    const terminal = new Terminal({
      theme: convertTheme(configuration.theme),
      fontSize: configuration.fontSize,
      fontFamily: configuration.fontFamily,
      scrollback: configuration.scrollback,
      bellStyle: configuration.bellStyle,
      cursorBlink: configuration.cursorBlink,
      cursorStyle: configuration.cursorStyle,
      allowTransparency: configuration.allowTransparency,
      convertEol: true,
      disableStdin: false,
      cols: 80,
      rows: 24
    });

    // Add addons
    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();

    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);

    // Open terminal in container
    terminal.open(terminalRef.current);

    // Store references
    xtermRef.current = terminal;
    fitAddonRef.current = fitAddon;
    webLinksAddonRef.current = webLinksAddon;

    // Setup event handlers
    setupTerminalEvents(terminal);

    // Fit terminal to container
    setTimeout(() => fitAddon.fit(), 100);

    // Setup mock terminal
    setupMockTerminal();

    // Setup resize observer
    const resizeObserver = new ResizeObserver(() => {
      setTimeout(() => fitAddon.fit(), 100);
    });
    resizeObserver.observe(terminalRef.current);

    return () => {
      terminal.dispose();
      resizeObserver.disconnect();
    };
  }, []);

  const setupTerminalEvents = (terminal: Terminal) => {
    terminal.onResize(({ cols, rows }) => {
      onResize?.({ cols, rows });
    });
  };

  const setupMockTerminal = () => {
    // Mock terminal for demonstration
    const terminal = xtermRef.current;
    if (!terminal) return;

    terminal.writeln('\x1b[32m✓\x1b[0m Welcome to PIB Agent Development Environment');
    terminal.writeln('\x1b[36mTerminal Integration Active\x1b[0m');
    terminal.writeln('');
    terminal.writeln('Available commands:');
    terminal.writeln('  \x1b[33mhelp\x1b[0m     - Show available commands');
    terminal.writeln('  \x1b[33mclear\x1b[0m    - Clear the terminal');
    terminal.writeln('  \x1b[33mdate\x1b[0m     - Show current date');
    terminal.writeln('  \x1b[33mecho\x1b[0m     - Echo text back');
    terminal.writeln('  \x1b[33mls\x1b[0m       - List directory contents');
    terminal.writeln('  \x1b[33mpwd\x1b[0m      - Show current directory');
    terminal.writeln('');
    terminal.write('\x1b[32m$\x1b[0m ');

    let currentCommand = '';
    terminal.onData((data) => {
      if (data === '\r') {
        terminal.writeln('');
        if (currentCommand.trim()) {
          handleMockCommand(currentCommand.trim());
          onCommand?.(currentCommand.trim());
        }
        currentCommand = '';
        terminal.write('\x1b[32m$\x1b[0m ');
      } else if (data === '\u007f') { // Backspace
        if (currentCommand.length > 0) {
          currentCommand = currentCommand.slice(0, -1);
          terminal.write('\b \b');
        }
      } else if (data === '\u0003') { // Ctrl+C
        terminal.writeln('^C');
        currentCommand = '';
        terminal.write('\x1b[32m$\x1b[0m ');
      } else {
        currentCommand += data;
        terminal.write(data);
      }
    });

    setIsConnected(true);
  };

  const handleMockCommand = (command: string) => {
    const terminal = xtermRef.current;
    if (!terminal) return;

    const args = command.split(' ');
    const cmd = args[0];

    switch (cmd) {
      case 'help':
        terminal.writeln('PIB Agent Development Environment Commands:');
        terminal.writeln('  \x1b[33mhelp\x1b[0m     - Show this help message');
        terminal.writeln('  \x1b[33mclear\x1b[0m    - Clear the terminal');
        terminal.writeln('  \x1b[33mdate\x1b[0m     - Show current date and time');
        terminal.writeln('  \x1b[33mecho\x1b[0m     - Echo text back');
        terminal.writeln('  \x1b[33mls\x1b[0m       - List directory contents');
        terminal.writeln('  \x1b[33mpwd\x1b[0m      - Show current working directory');
        terminal.writeln('  \x1b[33mwhoami\x1b[0m   - Show current user');
        terminal.writeln('  \x1b[33mnode -v\x1b[0m  - Show Node.js version');
        terminal.writeln('  \x1b[33mnpm -v\x1b[0m   - Show npm version');
        break;
      case 'clear':
        terminal.clear();
        terminal.writeln('\x1b[32m✓\x1b[0m Terminal cleared');
        break;
      case 'date':
        terminal.writeln(new Date().toString());
        break;
      case 'ls':
        terminal.writeln('agent-inbox/     apps/           packages/');
        terminal.writeln('src/            components/     lib/');
        terminal.writeln('README.md       package.json    tsconfig.json');
        break;
      case 'pwd':
        terminal.writeln('/workspace/pib-agent');
        break;
      case 'whoami':
        terminal.writeln('pib-developer');
        break;
      case 'node':
        if (args[1] === '-v') {
          terminal.writeln('v18.17.0');
        } else {
          terminal.writeln('Node.js REPL not available in demo mode');
        }
        break;
      case 'npm':
        if (args[1] === '-v') {
          terminal.writeln('9.6.7');
        } else {
          terminal.writeln('npm command not available in demo mode');
        }
        break;
      default:
        if (command.startsWith('echo ')) {
          terminal.writeln(command.substring(5));
        } else {
          terminal.writeln(`\x1b[31mCommand not found: ${command}\x1b[0m`);
          terminal.writeln('Type \x1b[33mhelp\x1b[0m for available commands');
        }
    }
  };

  const convertTheme = (theme: TerminalTheme) => {
    return {
      background: theme.background,
      foreground: theme.foreground,
      cursor: theme.cursor,
      selection: theme.selection,
      black: theme.black,
      red: theme.red,
      green: theme.green,
      yellow: theme.yellow,
      blue: theme.blue,
      magenta: theme.magenta,
      cyan: theme.cyan,
      white: theme.white,
      brightBlack: theme.brightBlack,
      brightRed: theme.brightRed,
      brightGreen: theme.brightGreen,
      brightYellow: theme.brightYellow,
      brightBlue: theme.brightBlue,
      brightMagenta: theme.brightMagenta,
      brightCyan: theme.brightCyan,
      brightWhite: theme.brightWhite,
    };
  };

  const handleCopyToClipboard = () => {
    const terminal = xtermRef.current;
    if (!terminal) return;

    const selection = terminal.getSelection();
    if (selection) {
      navigator.clipboard.writeText(selection);
    }
  };

  const handleClearTerminal = () => {
    const terminal = xtermRef.current;
    if (terminal) {
      terminal.clear();
      terminal.writeln('\x1b[32m✓\x1b[0m Terminal cleared');
      terminal.write('\x1b[32m$\x1b[0m ');
    }
  };

  const handleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  return (
    <div className={`web-terminal ${className} ${isMaximized ? 'fixed inset-0 z-50' : ''}`}>
      <div className="terminal-header flex items-center justify-between bg-gray-800 text-white px-4 py-2">
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm font-medium">
            {session?.name || 'Development Terminal'}
          </span>
          {session && (
            <Badge variant="secondary" className="text-xs">
              {session.shell}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopyToClipboard}
            className="text-white hover:bg-gray-700"
          >
            <Copy size={16} />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleMaximize}
            className="text-white hover:bg-gray-700"
          >
            {isMaximized ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="text-white hover:bg-gray-700">
                <MoreVertical size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleClearTerminal}>
                Clear Terminal
              </DropdownMenuItem>
              <DropdownMenuItem>
                Split Terminal
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-gray-700"
            >
              <X size={16} />
            </Button>
          )}
        </div>
      </div>
      
      <div 
        ref={terminalRef} 
        className="terminal-container flex-1"
        style={{ 
          backgroundColor: configuration.theme.background,
          height: isMaximized ? 'calc(100vh - 40px)' : '100%'
        }}
      />
      
      {session && (
        <div className="terminal-status-bar bg-gray-700 text-white px-4 py-1 text-xs flex justify-between">
          <span>
            {session.cwd}
          </span>
          <span>
            {session.shell} | {session.history.length} commands
          </span>
        </div>
      )}
    </div>
  );
};

export default WebTerminal;
