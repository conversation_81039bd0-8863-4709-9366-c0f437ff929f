// Terminal Session Types
export interface TerminalSession {
  id: string;
  name: string;
  shell: ShellType;
  cwd: string;
  environment: Record<string, string>;
  state: TerminalState;
  history: CommandHistory[];
  createdAt: Date;
  lastActivity: Date;
  userId: string;
}

export interface TerminalState {
  isActive: boolean;
  pid?: number;
  exitCode?: number;
  scrollPosition: number;
  selection?: SelectionRange;
  buffer: string;
}

export interface CommandHistory {
  command: string;
  timestamp: Date;
  cwd: string;
  exitCode?: number;
  duration?: number;
}

export interface SelectionRange {
  start: { row: number; col: number };
  end: { row: number; col: number };
}

export type ShellType = 'bash' | 'zsh' | 'sh' | 'fish' | 'powershell' | 'cmd';

export interface TerminalConfiguration {
  shell: ShellType;
  theme: TerminalTheme;
  fontSize: number;
  fontFamily: string;
  scrollback: number;
  bellStyle: 'none' | 'sound' | 'visual';
  cursorBlink: boolean;
  cursorStyle: 'block' | 'underline' | 'bar';
  allowTransparency: boolean;
}

export interface TerminalTheme {
  name?: string;
  background: string;
  foreground: string;
  cursor: string;
  cursorAccent?: string;
  selection: string;
  black: string;
  red: string;
  green: string;
  yellow: string;
  blue: string;
  magenta: string;
  cyan: string;
  white: string;
  brightBlack: string;
  brightRed: string;
  brightGreen: string;
  brightYellow: string;
  brightBlue: string;
  brightMagenta: string;
  brightCyan: string;
  brightWhite: string;
}
