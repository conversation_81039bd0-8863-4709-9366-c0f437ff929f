"use client";

import React, { useState, useEffect } from 'react';
import { Code, Terminal, FileText, Play, Save, GitBranch, Zap } from 'lucide-react';
import { MonacoEditor } from '@/components/code/editor/MonacoEditor';
import { FileExplorer } from '@/components/code/explorer/FileExplorer';
import { EditorTabs } from '@/components/code/tabs/EditorTabs';
import { StatusBar } from '@/components/code/status/StatusBar';
import { useEditorStore } from '@/store/code/editorStore';
import dynamic from 'next/dynamic';

// Dynamically import WebTerminal to avoid SSR issues
const WebTerminal = dynamic(() => import('@/components/terminal/web-terminal'), {
  ssr: false,
  loading: () => (
    <div className="h-full flex items-center justify-center bg-black text-white">
      <div className="text-center">
        <Terminal className="w-8 h-8 mx-auto mb-2 animate-pulse" />
        <p>Loading Terminal...</p>
      </div>
    </div>
  )
});

export default function CodeEditorPage() {
  const { getActiveFile, saveFile } = useEditorStore();
  const activeFile = getActiveFile();
  const [terminalSession, setTerminalSession] = useState(null);
  const [terminalConfig, setTerminalConfig] = useState({
    theme: {
      background: '#000000',
      foreground: '#ffffff',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selection: 'rgba(255, 255, 255, 0.3)',
      black: '#000000',
      red: '#cd3131',
      green: '#0dbc79',
      yellow: '#e5e510',
      blue: '#2472c8',
      magenta: '#bc3fbc',
      cyan: '#11a8cd',
      white: '#e5e5e5',
      brightBlack: '#666666',
      brightRed: '#f14c4c',
      brightGreen: '#23d18b',
      brightYellow: '#f5f543',
      brightBlue: '#3b8eea',
      brightMagenta: '#d670d6',
      brightCyan: '#29b8db',
      brightWhite: '#e5e5e5'
    },
    fontSize: 14,
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
    scrollback: 1000,
    bellStyle: 'none',
    cursorBlink: true,
    cursorStyle: 'block',
    allowTransparency: false,
    shell: process.platform === 'win32' ? 'cmd.exe' : '/bin/bash'
  });

  useEffect(() => {
    // Create a mock terminal session for demonstration
    const mockSession = {
      id: 'dev-terminal-1',
      name: 'Development Terminal',
      shell: terminalConfig.shell,
      cwd: '/workspace',
      environment: {},
      state: {
        isActive: true,
        scrollPosition: 0,
        buffer: ''
      },
      history: [],
      createdAt: new Date(),
      lastActivity: new Date(),
      userId: 'dev-user'
    };
    setTerminalSession(mockSession);
  }, [terminalConfig.shell]);

  const handleSave = () => {
    if (activeFile) {
      saveFile(activeFile.id);
    }
  };

  const handleRun = () => {
    console.log('Running code...');
    // TODO: Implement code execution
  };

  const handleTerminalCommand = (command: string) => {
    console.log('Terminal command:', command);
    // Handle terminal commands here
  };

  const handleTerminalResize = (size: { cols: number; rows: number }) => {
    console.log('Terminal resized:', size);
  };

  const handleSessionChange = (session: any) => {
    setTerminalSession(session);
  };

  return (
    <div className="flex flex-col h-full bg-gray-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <Code className="w-6 h-6 text-green-600" />
          </div>
          <div>
            <h1 className="text-xl font-bold">Development Environment</h1>
            <p className="text-sm text-gray-400">✅ Monaco Editor Integration - Full-featured code editor with syntax highlighting</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Zap className="w-4 h-4" />
            AI Assist
          </button>
          <button
            onClick={handleRun}
            className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Play className="w-4 h-4" />
            Run
          </button>
          <button
            onClick={handleSave}
            className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            disabled={!activeFile?.isDirty}
          >
            <Save className="w-4 h-4" />
            Save
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* File Explorer */}
        <FileExplorer className="w-64" />

        {/* Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Editor Tabs */}
          <EditorTabs />

          {/* Monaco Editor */}
          <MonacoEditor className="flex-1" />

          {/* Terminal */}
          <div className="h-64 border-t border-gray-700">
            {terminalSession ? (
              <WebTerminal
                session={terminalSession}
                configuration={terminalConfig}
                onCommand={handleTerminalCommand}
                onResize={handleTerminalResize}
                onSessionChange={handleSessionChange}
                className="h-full"
              />
            ) : (
              <div className="h-full bg-black flex items-center justify-center">
                <div className="text-center text-white">
                  <Terminal className="w-8 h-8 mx-auto mb-2 animate-pulse" />
                  <p>Initializing Terminal...</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* AI Assistant Panel */}
        <div className="w-80 bg-gray-800 border-l border-gray-700 p-4">
          <div className="flex items-center gap-2 mb-4">
            <Zap className="w-5 h-5 text-yellow-400" />
            <h3 className="font-semibold">AI Assistant</h3>
          </div>
          <div className="space-y-3">
            <div className="p-3 bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-300 mb-2">Suggestion:</div>
              <div className="text-sm">Add TypeScript interface for better type safety</div>
            </div>
            <div className="p-3 bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-300 mb-2">Code Review:</div>
              <div className="text-sm">Consider adding error handling for onClick</div>
            </div>
            <div className="p-3 bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-300 mb-2">Optimization:</div>
              <div className="text-sm">Use React.memo for performance</div>
            </div>
          </div>
          
          <div className="mt-6">
            <input 
              type="text" 
              placeholder="Ask AI about your code..." 
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400"
            />
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <StatusBar />
    </div>
  );
}
