"use client";

import React from 'react';
import { Code, Terminal, FileText, Play, Save, GitBranch, Zap } from 'lucide-react';
import { MonacoEditor } from '@/components/code/editor/MonacoEditor';
import { FileExplorer } from '@/components/code/explorer/FileExplorer';
import { EditorTabs } from '@/components/code/tabs/EditorTabs';
import { StatusBar } from '@/components/code/status/StatusBar';
import { useEditorStore } from '@/store/code/editorStore';

export default function CodeEditorPage() {
  const { getActiveFile, saveFile } = useEditorStore();
  const activeFile = getActiveFile();

  const handleSave = () => {
    if (activeFile) {
      saveFile(activeFile.id);
    }
  };

  const handleRun = () => {
    console.log('Running code...');
    // TODO: Implement code execution
  };

  return (
    <div className="flex flex-col h-full bg-gray-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <Code className="w-6 h-6 text-green-600" />
          </div>
          <div>
            <h1 className="text-xl font-bold">Development Environment</h1>
            <p className="text-sm text-gray-400">✅ Monaco Editor Integration - Full-featured code editor with syntax highlighting</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Zap className="w-4 h-4" />
            AI Assist
          </button>
          <button
            onClick={handleRun}
            className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Play className="w-4 h-4" />
            Run
          </button>
          <button
            onClick={handleSave}
            className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            disabled={!activeFile?.isDirty}
          >
            <Save className="w-4 h-4" />
            Save
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* File Explorer */}
        <FileExplorer className="w-64" />

        {/* Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Editor Tabs */}
          <EditorTabs />

          {/* Monaco Editor */}
          <MonacoEditor className="flex-1" />

          {/* Terminal */}
          <div className="h-48 bg-black border-t border-gray-700">
            <div className="flex items-center gap-2 px-4 py-2 bg-gray-800 border-b border-gray-700">
              <Terminal className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-medium">Terminal</span>
            </div>
            <div className="p-4 font-mono text-sm">
              <div className="text-green-400">$ npm run dev</div>
              <div className="text-gray-300">Starting development server...</div>
              <div className="text-gray-300">✓ Ready on http://localhost:3000</div>
              <div className="flex items-center">
                <span className="text-green-400">$ </span>
                <span className="bg-gray-700 w-2 h-4 ml-1 animate-pulse"></span>
              </div>
            </div>
          </div>
        </div>

        {/* AI Assistant Panel */}
        <div className="w-80 bg-gray-800 border-l border-gray-700 p-4">
          <div className="flex items-center gap-2 mb-4">
            <Zap className="w-5 h-5 text-yellow-400" />
            <h3 className="font-semibold">AI Assistant</h3>
          </div>
          <div className="space-y-3">
            <div className="p-3 bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-300 mb-2">Suggestion:</div>
              <div className="text-sm">Add TypeScript interface for better type safety</div>
            </div>
            <div className="p-3 bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-300 mb-2">Code Review:</div>
              <div className="text-sm">Consider adding error handling for onClick</div>
            </div>
            <div className="p-3 bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-300 mb-2">Optimization:</div>
              <div className="text-sm">Use React.memo for performance</div>
            </div>
          </div>
          
          <div className="mt-6">
            <input 
              type="text" 
              placeholder="Ask AI about your code..." 
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400"
            />
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <StatusBar />
    </div>
  );
}
