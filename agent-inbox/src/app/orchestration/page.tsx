"use client";

import React, { useEffect } from 'react';
import { Users, Plus, Settings, Activity, MessageSquare, Brain, Network, Server, Shield, Target, CheckCircle, RefreshCw, Heart, GitBranch, FileCheck } from 'lucide-react';
import { useOrchestrationStore } from '@/store/orchestration/orchestrationStore';
import RealTimeAgentMonitor from '@/components/orchestration/RealTimeAgentMonitor';
import AdvancedTaskOrchestration from '@/components/orchestration/AdvancedTaskOrchestration';
import AgentHealthMonitor from '@/components/orchestration/AgentHealthMonitor';
import QualityAssuranceDashboard from '@/components/orchestration/QualityAssuranceDashboard';

export default function OrchestrationPage() {
  const {
    agents,
    tasks,
    systemMetrics,
    networkStatus,
    activeTab,
    loading,
    errors,
    lastUpdated,
    setActiveTab,
    refreshAll,
    startRealTimeUpdates,
    stopRealTimeUpdates
  } = useOrchestrationStore();

  useEffect(() => {
    // Initial data fetch
    refreshAll();

    // Start real-time updates
    startRealTimeUpdates();

    // Cleanup on unmount
    return () => {
      stopRealTimeUpdates();
    };
  }, [refreshAll, startRealTimeUpdates, stopRealTimeUpdates]);

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <Users className="w-6 h-6 text-red-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Agent Orchestration</h1>
            <div className="flex items-center gap-4">
              <p className="text-sm text-gray-600">Multi-agent coordination system</p>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  systemMetrics.systemHealth === 'healthy' ? 'bg-green-500' :
                  systemMetrics.systemHealth === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <span className="text-xs text-gray-500 capitalize">{systemMetrics.systemHealth}</span>
              </div>
              {lastUpdated && (
                <span className="text-xs text-gray-400">
                  Updated: {lastUpdated.toLocaleTimeString()}
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={refreshAll}
            disabled={loading.agents || loading.tasks || loading.metrics}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading.agents || loading.tasks || loading.metrics ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
            <Plus className="w-4 h-4" />
            New Agent
          </button>
          <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Settings className="w-4 h-4" />
            Configure
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex items-center gap-1 p-4 bg-white border-b">
        {[
          { id: 'overview', label: 'Overview', icon: Activity },
          { id: 'agents', label: 'Agents', icon: Users },
          { id: 'tasks', label: 'Tasks', icon: Target },
          { id: 'monitoring', label: 'Monitoring', icon: Server },
          { id: 'quality', label: 'Quality', icon: CheckCircle },
        ].map((tab) => {
          const IconComponent = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-red-100 text-red-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <IconComponent className="w-4 h-4" />
              {tab.label}
            </button>
          );
        })}
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* System Status */}
            <div className="bg-white rounded-lg border p-6">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Activity className="w-5 h-5 text-red-600" />
                System Status
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Active Agents</span>
                  <span className={`font-semibold ${systemMetrics.activeAgents === systemMetrics.totalAgents ? 'text-green-600' : 'text-yellow-600'}`}>
                    {systemMetrics.activeAgents}/{systemMetrics.totalAgents}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Tasks Running</span>
                  <span className="font-semibold text-blue-600">{systemMetrics.tasksRunning}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Success Rate</span>
                  <span className={`font-semibold ${systemMetrics.successRate >= 90 ? 'text-green-600' : systemMetrics.successRate >= 70 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {systemMetrics.successRate}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Avg Response Time</span>
                  <span className="font-semibold text-blue-600">{systemMetrics.averageResponseTime.toFixed(1)}s</span>
                </div>
              </div>
            </div>

            {/* Agent Performance */}
            <div className="bg-white rounded-lg border p-6">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Brain className="w-5 h-5 text-purple-600" />
                Agent Performance
              </h3>
              <div className="space-y-3">
                {agents.map((agent) => (
                  <div key={agent.id} className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-sm">{agent.name}</div>
                      <div className="text-xs text-gray-500">{agent.tasks} tasks</div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-sm">{agent.performance}%</div>
                      <div className={`text-xs px-2 py-1 rounded-full ${
                        agent.status === 'active' ? 'bg-green-100 text-green-600' :
                        agent.status === 'busy' ? 'bg-yellow-100 text-yellow-600' :
                        'bg-gray-100 text-gray-600'
                      }`}>
                        {agent.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Network Status */}
            <div className="bg-white rounded-lg border p-6">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Network className="w-5 h-5 text-blue-600" />
                Network Status
              </h3>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    networkStatus.serviceDiscovery === 'online' ? 'bg-green-500' :
                    networkStatus.serviceDiscovery === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-sm">Service Discovery: {networkStatus.serviceDiscovery}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    networkStatus.loadBalancer === 'healthy' ? 'bg-green-500' :
                    networkStatus.loadBalancer === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-sm">Load Balancer: {networkStatus.loadBalancer}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    networkStatus.messageQueue === 'online' ? 'bg-green-500' :
                    networkStatus.messageQueue === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-sm">Message Queue: {networkStatus.messageQueue}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    networkStatus.database === 'connected' ? 'bg-green-500' :
                    networkStatus.database === 'slow' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-sm">Database: {networkStatus.database}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'agents' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {agents.map((agent) => (
              <div key={agent.id} className="bg-white rounded-lg border p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-gray-900">{agent.name}</h3>
                  <div className={`px-3 py-1 rounded-full text-sm ${
                    agent.status === 'active' ? 'bg-green-100 text-green-700' :
                    agent.status === 'busy' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {agent.status}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Type</span>
                    <span className="font-medium capitalize">{agent.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Active Tasks</span>
                    <span className="font-medium">{agent.tasks}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Performance</span>
                    <span className="font-medium">{Math.round(agent.performance)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Last Activity</span>
                    <span className="font-medium text-xs">
                      {new Date(agent.lastActivity).toLocaleTimeString()}
                    </span>
                  </div>
                  {agent.version && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Version</span>
                      <span className="font-medium text-xs">{agent.version}</span>
                    </div>
                  )}
                </div>
                <div className="mt-3">
                  <div className="text-sm text-gray-600 mb-1">Capabilities</div>
                  <div className="flex flex-wrap gap-1">
                    {agent.capabilities.slice(0, 3).map((capability) => (
                      <span key={capability} className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                        {capability}
                      </span>
                    ))}
                    {agent.capabilities.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                        +{agent.capabilities.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
                <div className="mt-4 flex gap-2">
                  <button className="px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200 transition-colors">
                    Configure
                  </button>
                  <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200 transition-colors">
                    Logs
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'tasks' && (
          <AdvancedTaskOrchestration />
        )}

        {activeTab === 'monitoring' && (
          <div className="space-y-6">
            <RealTimeAgentMonitor />
            <AgentHealthMonitor />
          </div>
        )}

        {activeTab === 'quality' && (
          <QualityAssuranceDashboard />
        )}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-6 py-3 bg-white border-t text-sm text-gray-600">
        <div>✅ Vibe-Kanban Integration Complete - Agent Orchestration v2.0</div>
        <div className="flex items-center gap-4">
          <div>Agents: {agents.length} active</div>
          <div>Tasks: {tasks.length} total</div>
          <div>Tab: {activeTab}</div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Vibe-Kanban Connected</span>
          </div>
        </div>
      </div>
    </div>
  );
}
