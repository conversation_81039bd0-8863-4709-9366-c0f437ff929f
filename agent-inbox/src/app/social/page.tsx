"use client";

import React, { useState } from 'react';
import { Share2, Calendar, BarChart3, Settings, Plus, Twitter, Facebook, Instagram, Linkedin } from 'lucide-react';
import { PlatformConnections } from '@/components/social/platforms/PlatformConnections';
import { PostComposer } from '@/components/social/composer/PostComposer';
import { AnalyticsDashboard } from '@/components/social/analytics/AnalyticsDashboard';
import { ContentCalendar } from '@/components/social/calendar/ContentCalendar';
import { useSocialStore } from '@/store/social/socialStore';

export default function SocialMediaPage() {
  const { activeTab, setActiveTab, connectedPlatforms, totalFollowers, totalReach, totalEngagement } = useSocialStore();
  const [showComposer, setShowComposer] = useState(false);

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'calendar', label: 'Calendar', icon: Calendar },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'compose', label: 'Compose', icon: Plus },
  ] as const;

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-pink-50 to-rose-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white/80 backdrop-blur-sm border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-pink-100 rounded-lg">
            <Share2 className="w-6 h-6 text-pink-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Social Media Hub</h1>
            <p className="text-sm text-gray-600">✅ API Integration Complete - Multi-platform management & analytics</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          {/* Quick Stats */}
          <div className="flex items-center gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-gray-900">{connectedPlatforms.length}</div>
              <div className="text-gray-500">Connected</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">{totalFollowers.toLocaleString()}</div>
              <div className="text-gray-500">Followers</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">{totalReach.toLocaleString()}</div>
              <div className="text-gray-500">Reach</div>
            </div>
          </div>

          <button
            onClick={() => setShowComposer(true)}
            className="flex items-center gap-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Create Post
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white/80 backdrop-blur-sm border-b">
        <div className="flex items-center px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-3 border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-pink-500 text-pink-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex gap-6 p-6">
        {/* Left Panel - Platform Connections */}
        <div className="w-80">
          <PlatformConnections />
        </div>

        {/* Right Panel - Tab Content */}
        <div className="flex-1">
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              <AnalyticsDashboard />
            </div>
          )}

          {activeTab === 'calendar' && (
            <ContentCalendar />
          )}

          {activeTab === 'analytics' && (
            <AnalyticsDashboard />
          )}

          {activeTab === 'compose' && (
            <PostComposer onClose={() => setActiveTab('dashboard')} />
          )}
        </div>
      </div>

      {/* Post Composer Modal */}
      {showComposer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <PostComposer onClose={() => setShowComposer(false)} />
          </div>
        </div>
      )}
    </div>
  );
}
