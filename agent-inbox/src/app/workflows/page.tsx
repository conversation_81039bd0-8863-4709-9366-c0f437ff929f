"use client";

import React, { useState } from 'react';
import { DndContext, DragEndEvent, DragOverEvent, DragStartEvent } from '@dnd-kit/core';
import { Workflow, Play, Save, Share2, Grid, Layers, Zap, ArrowRight, Code, FileText, Bot, Database, Plus } from 'lucide-react';
import { ComponentLibrary } from '@/components/workflows/library/ComponentLibrary';
import { WorkflowCanvas } from '@/components/workflows/canvas/WorkflowCanvas';
import WorkflowTemplates from '@/components/workflows/templates/WorkflowTemplates';
import { useWorkflowStore } from '@/store/workflows/workflowStore';

export default function WorkflowsPage() {
  const [showTemplates, setShowTemplates] = useState(false);

  const {
    workflows,
    activeWorkflow,
    setActiveWorkflow,
    createWorkflow,
    addNode,
    draggedComponent,
    setDraggedComponent,
    showPropertiesPanel,
    togglePropertiesPanel,
  } = useWorkflowStore();

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const component = active.data.current?.component;
    if (component) {
      setDraggedComponent(component);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && over.id === 'workflow-canvas') {
      const component = active.data.current?.component;
      if (component && activeWorkflow) {
        // Calculate drop position relative to canvas
        const rect = (over.rect as any);
        const position = {
          x: Math.max(50, Math.random() * 400 + 100), // Random position for now
          y: Math.max(50, Math.random() * 300 + 100),
        };
        
        addNode(component.id, position);
      }
    }
    
    setDraggedComponent(null);
  };

  const handleDragOver = (event: DragOverEvent) => {
    // Handle drag over events if needed
  };

  const handleCreateWorkflow = () => {
    const name = prompt('Enter workflow name:');
    if (name) {
      createWorkflow(name, 'New workflow created from Visual Workflows');
    }
  };

  return (
    <DndContext
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
    >
      <div className="flex flex-col h-full bg-gray-50">
        {/* Header */}
        <div className="flex items-center justify-between p-6 bg-white border-b">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <Workflow className="w-6 h-6 text-indigo-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Visual Workflows</h1>
              <p className="text-sm text-gray-600">🚀 Drag-Drop Integration Complete - Visual workflow builder with AI-powered components</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowTemplates(true)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Layers className="w-4 h-4" />
              Templates
            </button>
            <button
              onClick={handleCreateWorkflow}
              className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              New Workflow
            </button>
            <button 
              onClick={togglePropertiesPanel}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Share2 className="w-4 h-4" />
              Properties
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex">
          {/* Component Library */}
          <ComponentLibrary />

          {/* Workflow Canvas */}
          <WorkflowCanvas />
        </div>
      </div>

      {/* Templates Modal */}
      {showTemplates && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-6xl h-5/6 flex flex-col">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-semibold">Workflow Templates</h2>
              <button
                onClick={() => setShowTemplates(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            <div className="flex-1 overflow-hidden">
              <WorkflowTemplates />
            </div>
          </div>
        </div>
      )}
    </DndContext>
  );
}
