import { create } from 'zustand';

export interface SocialPlatform {
  id: string;
  name: 'twitter' | 'facebook' | 'instagram' | 'linkedin';
  displayName: string;
  connected: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: number;
  userId?: string;
  username?: string;
  profilePicture?: string;
  followers: number;
  following: number;
  posts: number;
  lastSync: number;
}

export interface SocialPost {
  id: string;
  content: string;
  platforms: string[];
  scheduledAt?: number;
  publishedAt?: number;
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  mediaUrls: string[];
  hashtags: string[];
  mentions: string[];
  analytics?: {
    likes: number;
    shares: number;
    comments: number;
    reach: number;
    impressions: number;
  };
  error?: string;
}

export interface SocialAnalytics {
  platformId: string;
  period: 'day' | 'week' | 'month' | 'year';
  metrics: {
    followers: number;
    followersChange: number;
    reach: number;
    reachChange: number;
    engagement: number;
    engagementRate: number;
    posts: number;
    topPost?: SocialPost;
  };
}

export interface SocialSettings {
  autoHashtags: boolean;
  defaultScheduleTime: string;
  crossPostEnabled: boolean;
  analyticsRefreshInterval: number;
  notificationsEnabled: boolean;
}

export interface SocialState {
  // Platforms
  platforms: SocialPlatform[];
  connectedPlatforms: SocialPlatform[];
  
  // Posts
  posts: SocialPost[];
  scheduledPosts: SocialPost[];
  recentPosts: SocialPost[];
  
  // Analytics
  analytics: SocialAnalytics[];
  totalFollowers: number;
  totalReach: number;
  totalEngagement: number;
  
  // UI State
  selectedPlatforms: string[];
  activeTab: 'dashboard' | 'calendar' | 'analytics' | 'compose';
  isComposing: boolean;
  isPublishing: boolean;
  
  // Settings
  settings: SocialSettings;
  
  // Actions
  connectPlatform: (platform: string, credentials: any) => Promise<void>;
  disconnectPlatform: (platformId: string) => void;
  refreshPlatformData: (platformId: string) => Promise<void>;
  
  createPost: (content: string, platforms: string[], mediaUrls?: string[]) => string;
  schedulePost: (postId: string, scheduledAt: number) => void;
  publishPost: (postId: string) => Promise<void>;
  deletePost: (postId: string) => void;
  updatePost: (postId: string, updates: Partial<SocialPost>) => void;
  
  loadAnalytics: (platformId?: string, period?: 'day' | 'week' | 'month') => Promise<void>;
  refreshAnalytics: () => Promise<void>;
  
  setActiveTab: (tab: 'dashboard' | 'calendar' | 'analytics' | 'compose') => void;
  setSelectedPlatforms: (platforms: string[]) => void;
  updateSettings: (settings: Partial<SocialSettings>) => void;
  
  // Getters
  getPlatformById: (id: string) => SocialPlatform | null;
  getPostsByPlatform: (platformId: string) => SocialPost[];
  getAnalyticsByPlatform: (platformId: string) => SocialAnalytics | null;
}

const defaultSettings: SocialSettings = {
  autoHashtags: true,
  defaultScheduleTime: '09:00',
  crossPostEnabled: false,
  analyticsRefreshInterval: 300000, // 5 minutes
  notificationsEnabled: true,
};

// Mock data for demonstration
const mockPlatforms: SocialPlatform[] = [
  {
    id: 'twitter_1',
    name: 'twitter',
    displayName: 'Twitter',
    connected: true,
    username: '@yourcompany',
    followers: 12500,
    following: 850,
    posts: 1240,
    lastSync: Date.now() - 120000, // 2 minutes ago
  },
  {
    id: 'facebook_1',
    name: 'facebook',
    displayName: 'Facebook',
    connected: true,
    username: 'Your Company',
    followers: 8200,
    following: 0,
    posts: 456,
    lastSync: Date.now() - 180000, // 3 minutes ago
  },
  {
    id: 'instagram_1',
    name: 'instagram',
    displayName: 'Instagram',
    connected: false,
    username: '',
    followers: 0,
    following: 0,
    posts: 0,
    lastSync: 0,
  },
  {
    id: 'linkedin_1',
    name: 'linkedin',
    displayName: 'LinkedIn',
    connected: true,
    username: 'Your Company',
    followers: 3100,
    following: 250,
    posts: 89,
    lastSync: Date.now() - 240000, // 4 minutes ago
  },
];

const mockPosts: SocialPost[] = [
  {
    id: 'post_1',
    content: 'Just launched our new AI feature! 🚀 Excited to see how it helps our users be more productive.',
    platforms: ['twitter_1'],
    publishedAt: Date.now() - 7200000, // 2 hours ago
    status: 'published',
    mediaUrls: [],
    hashtags: ['#AI', '#productivity', '#launch'],
    mentions: [],
    analytics: {
      likes: 24,
      shares: 5,
      comments: 3,
      reach: 1250,
      impressions: 2100,
    },
  },
  {
    id: 'post_2',
    content: 'Behind the scenes of our development process. Here\'s how we build features that matter.',
    platforms: ['facebook_1'],
    publishedAt: Date.now() - 14400000, // 4 hours ago
    status: 'published',
    mediaUrls: [],
    hashtags: ['#development', '#process'],
    mentions: [],
    analytics: {
      likes: 12,
      shares: 2,
      comments: 3,
      reach: 890,
      impressions: 1450,
    },
  },
  {
    id: 'post_3',
    content: 'Thoughts on the future of AI in productivity tools. What features would you like to see?',
    platforms: ['linkedin_1'],
    publishedAt: Date.now() - 86400000, // 1 day ago
    status: 'published',
    mediaUrls: [],
    hashtags: ['#AI', '#productivity', '#future'],
    mentions: [],
    analytics: {
      likes: 45,
      shares: 8,
      comments: 12,
      reach: 2100,
      impressions: 3200,
    },
  },
];

const mockScheduledPosts: SocialPost[] = [
  {
    id: 'scheduled_1',
    content: 'Weekly product update thread: Here\'s what we shipped this week...',
    platforms: ['twitter_1'],
    scheduledAt: Date.now() + 10800000, // 3 hours from now
    status: 'scheduled',
    mediaUrls: [],
    hashtags: ['#update', '#weekly'],
    mentions: [],
  },
  {
    id: 'scheduled_2',
    content: 'Industry insights: The latest trends in AI and automation...',
    platforms: ['linkedin_1'],
    scheduledAt: Date.now() + 97200000, // tomorrow 9 AM
    status: 'scheduled',
    mediaUrls: [],
    hashtags: ['#insights', '#AI', '#automation'],
    mentions: [],
  },
  {
    id: 'scheduled_3',
    content: 'Community spotlight: Featuring amazing work from our users...',
    platforms: ['facebook_1'],
    scheduledAt: Date.now() + 115200000, // tomorrow 2 PM
    status: 'scheduled',
    mediaUrls: [],
    hashtags: ['#community', '#spotlight'],
    mentions: [],
  },
];

export const useSocialStore = create<SocialState>((set, get) => ({
  // Initial state
  platforms: mockPlatforms,
  connectedPlatforms: mockPlatforms.filter(p => p.connected),
  posts: [...mockPosts, ...mockScheduledPosts],
  scheduledPosts: mockScheduledPosts,
  recentPosts: mockPosts,
  analytics: [],
  totalFollowers: mockPlatforms.reduce((sum, p) => sum + p.followers, 0),
  totalReach: 4240,
  totalEngagement: 156,
  selectedPlatforms: [],
  activeTab: 'dashboard',
  isComposing: false,
  isPublishing: false,
  settings: defaultSettings,

  // Platform actions
  connectPlatform: async (platform, credentials) => {
    try {
      console.log(`Connecting to ${platform}...`, credentials);
      
      // Mock connection process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      set((state) => ({
        platforms: state.platforms.map(p =>
          p.name === platform
            ? { ...p, connected: true, lastSync: Date.now() }
            : p
        ),
        connectedPlatforms: state.platforms.filter(p => 
          p.connected || p.name === platform
        ),
      }));
    } catch (error) {
      console.error(`Failed to connect to ${platform}:`, error);
    }
  },

  disconnectPlatform: (platformId) => {
    set((state) => ({
      platforms: state.platforms.map(p =>
        p.id === platformId
          ? { ...p, connected: false, accessToken: undefined }
          : p
      ),
      connectedPlatforms: state.platforms.filter(p => 
        p.connected && p.id !== platformId
      ),
    }));
  },

  refreshPlatformData: async (platformId) => {
    try {
      console.log(`Refreshing data for platform ${platformId}...`);
      
      // Mock refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set((state) => ({
        platforms: state.platforms.map(p =>
          p.id === platformId
            ? { ...p, lastSync: Date.now() }
            : p
        ),
      }));
    } catch (error) {
      console.error(`Failed to refresh platform ${platformId}:`, error);
    }
  },

  // Post actions
  createPost: (content, platforms, mediaUrls = []) => {
    const postId = `post_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newPost: SocialPost = {
      id: postId,
      content,
      platforms,
      status: 'draft',
      mediaUrls,
      hashtags: content.match(/#\w+/g) || [],
      mentions: content.match(/@\w+/g) || [],
    };

    set((state) => ({
      posts: [...state.posts, newPost],
    }));

    return postId;
  },

  schedulePost: (postId, scheduledAt) => {
    set((state) => ({
      posts: state.posts.map(post =>
        post.id === postId
          ? { ...post, scheduledAt, status: 'scheduled' as const }
          : post
      ),
      scheduledPosts: [
        ...state.scheduledPosts,
        ...state.posts.filter(p => p.id === postId)
      ],
    }));
  },

  publishPost: async (postId) => {
    set({ isPublishing: true });
    
    try {
      console.log(`Publishing post ${postId}...`);
      
      // Mock publishing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      set((state) => ({
        posts: state.posts.map(post =>
          post.id === postId
            ? { ...post, status: 'published' as const, publishedAt: Date.now() }
            : post
        ),
        recentPosts: [
          ...state.posts.filter(p => p.id === postId),
          ...state.recentPosts
        ].slice(0, 10),
        isPublishing: false,
      }));
    } catch (error) {
      console.error(`Failed to publish post ${postId}:`, error);
      set({ isPublishing: false });
    }
  },

  deletePost: (postId) => {
    set((state) => ({
      posts: state.posts.filter(p => p.id !== postId),
      scheduledPosts: state.scheduledPosts.filter(p => p.id !== postId),
      recentPosts: state.recentPosts.filter(p => p.id !== postId),
    }));
  },

  updatePost: (postId, updates) => {
    set((state) => ({
      posts: state.posts.map(post =>
        post.id === postId ? { ...post, ...updates } : post
      ),
    }));
  },

  // Analytics actions
  loadAnalytics: async (platformId, period = 'week') => {
    try {
      console.log(`Loading analytics for ${platformId || 'all platforms'}...`);
      
      // Mock analytics loading
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock analytics data would be loaded here
    } catch (error) {
      console.error('Failed to load analytics:', error);
    }
  },

  refreshAnalytics: async () => {
    try {
      console.log('Refreshing analytics...');
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Failed to refresh analytics:', error);
    }
  },

  // UI actions
  setActiveTab: (tab) => {
    set({ activeTab: tab });
  },

  setSelectedPlatforms: (platforms) => {
    set({ selectedPlatforms: platforms });
  },

  updateSettings: (newSettings) => {
    set((state) => ({
      settings: { ...state.settings, ...newSettings },
    }));
  },

  // Getters
  getPlatformById: (id) => {
    const state = get();
    return state.platforms.find(p => p.id === id) || null;
  },

  getPostsByPlatform: (platformId) => {
    const state = get();
    return state.posts.filter(post => post.platforms.includes(platformId));
  },

  getAnalyticsByPlatform: (platformId) => {
    const state = get();
    return state.analytics.find(a => a.platformId === platformId) || null;
  },
}));
