import { create } from 'zustand';

export interface WorkflowNode {
  id: string;
  type: 'input' | 'output' | 'processing' | 'ai' | 'integration';
  category: string;
  name: string;
  description: string;
  position: { x: number; y: number };
  data: Record<string, any>;
  inputs: WorkflowPort[];
  outputs: WorkflowPort[];
  config: Record<string, any>;
  status: 'idle' | 'running' | 'completed' | 'error';
  error?: string;
}

export interface WorkflowPort {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'any';
  required: boolean;
  description: string;
}

export interface WorkflowConnection {
  id: string;
  sourceNodeId: string;
  sourcePortId: string;
  targetNodeId: string;
  targetPortId: string;
  data?: any;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  thumbnail?: string;
  tags: string[];
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  status: 'draft' | 'active' | 'paused' | 'completed' | 'error';
  createdAt: number;
  updatedAt: number;
  lastRun?: number;
  runCount: number;
  variables: Record<string, any>;
  settings: WorkflowSettings;
}

export interface WorkflowSettings {
  autoSave: boolean;
  debugMode: boolean;
  timeout: number;
  retryCount: number;
  parallelExecution: boolean;
}

export interface ComponentDefinition {
  id: string;
  name: string;
  description: string;
  category: 'input' | 'output' | 'processing' | 'ai' | 'integration';
  icon: string;
  color: string;
  inputs: WorkflowPort[];
  outputs: WorkflowPort[];
  configSchema: Record<string, any>;
  defaultConfig: Record<string, any>;
  executable: boolean;
}

export interface WorkflowState {
  // Workflows
  workflows: Workflow[];
  activeWorkflow: Workflow | null;
  
  // Components and Templates
  componentDefinitions: ComponentDefinition[];
  templates: WorkflowTemplate[];
  
  // Canvas State
  selectedNodes: string[];
  selectedConnections: string[];
  canvasPosition: { x: number; y: number };
  canvasZoom: number;
  
  // Execution State
  isExecuting: boolean;
  executionResults: Record<string, any>;
  executionLogs: string[];
  
  // UI State
  showComponentLibrary: boolean;
  showPropertiesPanel: boolean;
  draggedComponent: ComponentDefinition | null;

  // Connection Creation State
  connectionCreation: {
    isCreating: boolean;
    sourceNodeId: string | null;
    sourcePortId: string | null;
    isSourceOutput: boolean;
    mousePosition: { x: number; y: number };
  } | null;
  
  // Actions
  createWorkflow: (name: string, description?: string) => string;
  updateWorkflow: (id: string, updates: Partial<Workflow>) => void;
  deleteWorkflow: (id: string) => void;
  setActiveWorkflow: (workflow: Workflow | null) => void;
  
  addNode: (componentId: string, position: { x: number; y: number }) => string;
  updateNode: (nodeId: string, updates: Partial<WorkflowNode>) => void;
  deleteNode: (nodeId: string) => void;
  moveNode: (nodeId: string, position: { x: number; y: number }) => void;
  
  addConnection: (connection: Omit<WorkflowConnection, 'id'>) => string;
  deleteConnection: (connectionId: string) => void;
  
  executeWorkflow: (workflowId?: string) => Promise<void>;
  stopExecution: () => void;
  
  setSelectedNodes: (nodeIds: string[]) => void;
  setSelectedConnections: (connectionIds: string[]) => void;
  setCanvasPosition: (position: { x: number; y: number }) => void;
  setCanvasZoom: (zoom: number) => void;
  
  setDraggedComponent: (component: ComponentDefinition | null) => void;
  toggleComponentLibrary: () => void;
  togglePropertiesPanel: () => void;

  // Connection Creation Actions
  startConnectionCreation: (nodeId: string, portId: string, isOutput: boolean, mousePosition: { x: number; y: number }) => void;
  updateConnectionCreation: (mousePosition: { x: number; y: number }) => void;
  completeConnectionCreation: (targetNodeId: string, targetPortId: string, isTargetOutput: boolean) => boolean;
  cancelConnectionCreation: () => void;
  
  // Getters
  getNodeById: (id: string) => WorkflowNode | null;
  getConnectionById: (id: string) => WorkflowConnection | null;
  getComponentDefinition: (id: string) => ComponentDefinition | null;
  getNodeConnections: (nodeId: string) => WorkflowConnection[];
}

// Mock component definitions
const mockComponentDefinitions: ComponentDefinition[] = [
  {
    id: 'text-input',
    name: 'Text Input',
    description: 'Accept text input from user',
    category: 'input',
    icon: 'Type',
    color: 'blue',
    inputs: [],
    outputs: [
      { id: 'text', name: 'Text', type: 'string', required: true, description: 'Input text' }
    ],
    configSchema: {
      placeholder: { type: 'string', default: 'Enter text...' },
      multiline: { type: 'boolean', default: false }
    },
    defaultConfig: { placeholder: 'Enter text...', multiline: false },
    executable: true,
  },
  {
    id: 'openai-chat',
    name: 'OpenAI Chat',
    description: 'Process text with OpenAI GPT',
    category: 'ai',
    icon: 'Bot',
    color: 'purple',
    inputs: [
      { id: 'prompt', name: 'Prompt', type: 'string', required: true, description: 'Input prompt' },
      { id: 'context', name: 'Context', type: 'string', required: false, description: 'Additional context' }
    ],
    outputs: [
      { id: 'response', name: 'Response', type: 'string', required: true, description: 'AI response' }
    ],
    configSchema: {
      model: { type: 'string', default: 'gpt-3.5-turbo', options: ['gpt-3.5-turbo', 'gpt-4'] },
      temperature: { type: 'number', default: 0.7, min: 0, max: 2 },
      maxTokens: { type: 'number', default: 1000, min: 1, max: 4000 }
    },
    defaultConfig: { model: 'gpt-3.5-turbo', temperature: 0.7, maxTokens: 1000 },
    executable: true,
  },
  {
    id: 'text-output',
    name: 'Text Output',
    description: 'Display text output',
    category: 'output',
    icon: 'FileText',
    color: 'green',
    inputs: [
      { id: 'text', name: 'Text', type: 'string', required: true, description: 'Text to display' }
    ],
    outputs: [],
    configSchema: {
      format: { type: 'string', default: 'plain', options: ['plain', 'markdown', 'html'] }
    },
    defaultConfig: { format: 'plain' },
    executable: true,
  },
  {
    id: 'data-filter',
    name: 'Data Filter',
    description: 'Filter data based on conditions',
    category: 'processing',
    icon: 'Filter',
    color: 'orange',
    inputs: [
      { id: 'data', name: 'Data', type: 'array', required: true, description: 'Input data array' },
      { id: 'condition', name: 'Condition', type: 'string', required: true, description: 'Filter condition' }
    ],
    outputs: [
      { id: 'filtered', name: 'Filtered Data', type: 'array', required: true, description: 'Filtered results' }
    ],
    configSchema: {
      operator: { type: 'string', default: 'equals', options: ['equals', 'contains', 'greater', 'less'] }
    },
    defaultConfig: { operator: 'equals' },
    executable: true,
  },
];

// Mock workflows
const mockWorkflows: Workflow[] = [
  {
    id: 'workflow-1',
    name: 'Content Generation Pipeline',
    description: 'Generate content using AI and process it',
    nodes: [
      {
        id: 'node-1',
        type: 'input',
        category: 'input',
        name: 'Text Input',
        description: 'User input',
        position: { x: 100, y: 100 },
        data: {},
        inputs: [],
        outputs: [{ id: 'text', name: 'Text', type: 'string', required: true, description: 'Input text' }],
        config: { placeholder: 'Enter your topic...' },
        status: 'idle',
      },
      {
        id: 'node-2',
        type: 'ai',
        category: 'ai',
        name: 'OpenAI Chat',
        description: 'AI processing',
        position: { x: 400, y: 100 },
        data: {},
        inputs: [{ id: 'prompt', name: 'Prompt', type: 'string', required: true, description: 'Input prompt' }],
        outputs: [{ id: 'response', name: 'Response', type: 'string', required: true, description: 'AI response' }],
        config: { model: 'gpt-3.5-turbo', temperature: 0.7 },
        status: 'idle',
      },
      {
        id: 'node-3',
        type: 'output',
        category: 'output',
        name: 'Text Output',
        description: 'Display result',
        position: { x: 700, y: 100 },
        data: {},
        inputs: [{ id: 'text', name: 'Text', type: 'string', required: true, description: 'Text to display' }],
        outputs: [],
        config: { format: 'markdown' },
        status: 'idle',
      },
    ],
    connections: [
      {
        id: 'conn-1',
        sourceNodeId: 'node-1',
        sourcePortId: 'text',
        targetNodeId: 'node-2',
        targetPortId: 'prompt',
      },
      {
        id: 'conn-2',
        sourceNodeId: 'node-2',
        sourcePortId: 'response',
        targetNodeId: 'node-3',
        targetPortId: 'text',
      },
    ],
    status: 'draft',
    createdAt: Date.now() - 86400000,
    updatedAt: Date.now() - 3600000,
    runCount: 0,
    variables: {},
    settings: {
      autoSave: true,
      debugMode: false,
      timeout: 30000,
      retryCount: 3,
      parallelExecution: false,
    },
  },
];

const defaultSettings: WorkflowSettings = {
  autoSave: true,
  debugMode: false,
  timeout: 30000,
  retryCount: 3,
  parallelExecution: false,
};

export const useWorkflowStore = create<WorkflowState>((set, get) => ({
  // Initial state
  workflows: mockWorkflows,
  activeWorkflow: null,
  componentDefinitions: mockComponentDefinitions,
  templates: [],
  selectedNodes: [],
  selectedConnections: [],
  canvasPosition: { x: 0, y: 0 },
  canvasZoom: 1,
  isExecuting: false,
  executionResults: {},
  executionLogs: [],
  showComponentLibrary: true,
  showPropertiesPanel: true,
  draggedComponent: null,
  connectionCreation: null,

  // Workflow actions
  createWorkflow: (name, description = '') => {
    const id = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newWorkflow: Workflow = {
      id,
      name,
      description,
      nodes: [],
      connections: [],
      status: 'draft',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      runCount: 0,
      variables: {},
      settings: { ...defaultSettings },
    };

    set((state) => ({
      workflows: [...state.workflows, newWorkflow],
      activeWorkflow: newWorkflow,
    }));

    return id;
  },

  updateWorkflow: (id, updates) => {
    set((state) => ({
      workflows: state.workflows.map(workflow =>
        workflow.id === id
          ? { ...workflow, ...updates, updatedAt: Date.now() }
          : workflow
      ),
      activeWorkflow: state.activeWorkflow?.id === id
        ? { ...state.activeWorkflow, ...updates, updatedAt: Date.now() }
        : state.activeWorkflow,
    }));
  },

  deleteWorkflow: (id) => {
    set((state) => ({
      workflows: state.workflows.filter(w => w.id !== id),
      activeWorkflow: state.activeWorkflow?.id === id ? null : state.activeWorkflow,
    }));
  },

  setActiveWorkflow: (workflow) => {
    set({ activeWorkflow: workflow });
  },

  // Node actions
  addNode: (componentId, position) => {
    const component = get().getComponentDefinition(componentId);
    if (!component) return '';

    const nodeId = `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newNode: WorkflowNode = {
      id: nodeId,
      type: component.category,
      category: component.category,
      name: component.name,
      description: component.description,
      position,
      data: {},
      inputs: [...component.inputs],
      outputs: [...component.outputs],
      config: { ...component.defaultConfig },
      status: 'idle',
    };

    const { activeWorkflow } = get();
    if (activeWorkflow) {
      const updatedWorkflow = {
        ...activeWorkflow,
        nodes: [...activeWorkflow.nodes, newNode],
        updatedAt: Date.now(),
      };

      set((state) => ({
        workflows: state.workflows.map(w =>
          w.id === activeWorkflow.id ? updatedWorkflow : w
        ),
        activeWorkflow: updatedWorkflow,
      }));
    }

    return nodeId;
  },

  updateNode: (nodeId, updates) => {
    const { activeWorkflow } = get();
    if (!activeWorkflow) return;

    const updatedWorkflow = {
      ...activeWorkflow,
      nodes: activeWorkflow.nodes.map(node =>
        node.id === nodeId ? { ...node, ...updates } : node
      ),
      updatedAt: Date.now(),
    };

    set((state) => ({
      workflows: state.workflows.map(w =>
        w.id === activeWorkflow.id ? updatedWorkflow : w
      ),
      activeWorkflow: updatedWorkflow,
    }));
  },

  deleteNode: (nodeId) => {
    const { activeWorkflow } = get();
    if (!activeWorkflow) return;

    const updatedWorkflow = {
      ...activeWorkflow,
      nodes: activeWorkflow.nodes.filter(node => node.id !== nodeId),
      connections: activeWorkflow.connections.filter(conn =>
        conn.sourceNodeId !== nodeId && conn.targetNodeId !== nodeId
      ),
      updatedAt: Date.now(),
    };

    set((state) => ({
      workflows: state.workflows.map(w =>
        w.id === activeWorkflow.id ? updatedWorkflow : w
      ),
      activeWorkflow: updatedWorkflow,
      selectedNodes: state.selectedNodes.filter(id => id !== nodeId),
    }));
  },

  moveNode: (nodeId, position) => {
    get().updateNode(nodeId, { position });
  },

  // Connection actions
  addConnection: (connection) => {
    const connectionId = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newConnection: WorkflowConnection = {
      ...connection,
      id: connectionId,
    };

    const { activeWorkflow } = get();
    if (!activeWorkflow) return '';

    const updatedWorkflow = {
      ...activeWorkflow,
      connections: [...activeWorkflow.connections, newConnection],
      updatedAt: Date.now(),
    };

    set((state) => ({
      workflows: state.workflows.map(w =>
        w.id === activeWorkflow.id ? updatedWorkflow : w
      ),
      activeWorkflow: updatedWorkflow,
    }));

    return connectionId;
  },

  deleteConnection: (connectionId) => {
    const { activeWorkflow } = get();
    if (!activeWorkflow) return;

    const updatedWorkflow = {
      ...activeWorkflow,
      connections: activeWorkflow.connections.filter(conn => conn.id !== connectionId),
      updatedAt: Date.now(),
    };

    set((state) => ({
      workflows: state.workflows.map(w =>
        w.id === activeWorkflow.id ? updatedWorkflow : w
      ),
      activeWorkflow: updatedWorkflow,
      selectedConnections: state.selectedConnections.filter(id => id !== connectionId),
    }));
  },

  // Execution actions
  executeWorkflow: async (workflowId) => {
    const workflow = workflowId
      ? get().workflows.find(w => w.id === workflowId)
      : get().activeWorkflow;

    if (!workflow) return;

    set({ isExecuting: true, executionLogs: [], executionResults: {} });

    try {
      console.log(`Executing workflow: ${workflow.name}`);
      
      // Mock execution - in real implementation, this would process nodes
      for (const node of workflow.nodes) {
        set((state) => ({
          executionLogs: [...state.executionLogs, `Executing node: ${node.name}`],
        }));

        get().updateNode(node.id, { status: 'running' });
        
        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        get().updateNode(node.id, { status: 'completed' });
        
        set((state) => ({
          executionResults: {
            ...state.executionResults,
            [node.id]: { success: true, output: `Result from ${node.name}` },
          },
        }));
      }

      get().updateWorkflow(workflow.id, {
        status: 'completed',
        lastRun: Date.now(),
        runCount: workflow.runCount + 1,
      });

      set((state) => ({
        executionLogs: [...state.executionLogs, 'Workflow execution completed successfully'],
      }));

    } catch (error) {
      console.error('Workflow execution failed:', error);
      set((state) => ({
        executionLogs: [...state.executionLogs, `Execution failed: ${error}`],
      }));
    } finally {
      set({ isExecuting: false });
    }
  },

  stopExecution: () => {
    set({ isExecuting: false });
  },

  // UI actions
  setSelectedNodes: (nodeIds) => {
    set({ selectedNodes: nodeIds });
  },

  setSelectedConnections: (connectionIds) => {
    set({ selectedConnections: connectionIds });
  },

  setCanvasPosition: (position) => {
    set({ canvasPosition: position });
  },

  setCanvasZoom: (zoom) => {
    set({ canvasZoom: zoom });
  },

  setDraggedComponent: (component) => {
    set({ draggedComponent: component });
  },

  toggleComponentLibrary: () => {
    set((state) => ({ showComponentLibrary: !state.showComponentLibrary }));
  },

  togglePropertiesPanel: () => {
    set((state) => ({ showPropertiesPanel: !state.showPropertiesPanel }));
  },

  // Getters
  getNodeById: (id) => {
    const { activeWorkflow } = get();
    return activeWorkflow?.nodes.find(node => node.id === id) || null;
  },

  getConnectionById: (id) => {
    const { activeWorkflow } = get();
    return activeWorkflow?.connections.find(conn => conn.id === id) || null;
  },

  getComponentDefinition: (id) => {
    return get().componentDefinitions.find(comp => comp.id === id) || null;
  },

  getNodeConnections: (nodeId) => {
    const { activeWorkflow } = get();
    if (!activeWorkflow) return [];

    return activeWorkflow.connections.filter(conn =>
      conn.sourceNodeId === nodeId || conn.targetNodeId === nodeId
    );
  },

  // Connection Creation Actions
  startConnectionCreation: (nodeId, portId, isOutput, mousePosition) => {
    set({
      connectionCreation: {
        isCreating: true,
        sourceNodeId: nodeId,
        sourcePortId: portId,
        isSourceOutput: isOutput,
        mousePosition,
      },
    });
  },

  updateConnectionCreation: (mousePosition) => {
    const { connectionCreation } = get();
    if (connectionCreation) {
      set({
        connectionCreation: {
          ...connectionCreation,
          mousePosition,
        },
      });
    }
  },

  completeConnectionCreation: (targetNodeId, targetPortId, isTargetOutput) => {
    const { connectionCreation, addConnection } = get();
    if (!connectionCreation) return false;

    const { sourceNodeId, sourcePortId, isSourceOutput } = connectionCreation;

    // Validation: source must be output, target must be input (or vice versa)
    if (isSourceOutput === isTargetOutput) {
      set({ connectionCreation: null });
      return false;
    }

    // Validation: can't connect node to itself
    if (sourceNodeId === targetNodeId) {
      set({ connectionCreation: null });
      return false;
    }

    // Create the connection
    const connection = isSourceOutput
      ? {
          sourceNodeId,
          sourcePortId,
          targetNodeId,
          targetPortId,
        }
      : {
          sourceNodeId: targetNodeId,
          sourcePortId: targetPortId,
          targetNodeId: sourceNodeId,
          targetPortId: sourcePortId,
        };

    addConnection(connection);
    set({ connectionCreation: null });
    return true;
  },

  cancelConnectionCreation: () => {
    set({ connectionCreation: null });
  },
}));
