import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface Agent {
  id: string;
  name: string;
  type: 'ai' | 'human' | 'system' | 'hybrid';
  status: 'active' | 'idle' | 'busy' | 'offline' | 'error';
  tasks: number;
  performance: number;
  capabilities: string[];
  lastActivity: Date;
  endpoint?: string;
  version?: string;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  agentId: string;
  agentName: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'queued' | 'in-progress' | 'completed' | 'failed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  estimatedDuration?: number;
  actualDuration?: number;
}

export interface SystemMetrics {
  activeAgents: number;
  totalAgents: number;
  tasksRunning: number;
  totalTasks: number;
  successRate: number;
  averageResponseTime: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  uptime: number;
}

export interface NetworkStatus {
  serviceDiscovery: 'online' | 'offline' | 'warning';
  loadBalancer: 'healthy' | 'warning' | 'critical';
  messageQueue: 'online' | 'warning' | 'offline';
  database: 'connected' | 'disconnected' | 'slow';
}

export interface OrchestrationState {
  // Data
  agents: Agent[];
  tasks: Task[];
  systemMetrics: SystemMetrics;
  networkStatus: NetworkStatus;
  
  // UI State
  activeTab: string;
  selectedAgent: string | null;
  selectedTask: string | null;
  
  // Loading states
  loading: {
    agents: boolean;
    tasks: boolean;
    metrics: boolean;
  };
  
  // Error states
  errors: {
    agents: string | null;
    tasks: string | null;
    metrics: string | null;
  };
  
  // Real-time updates
  lastUpdated: Date | null;
  updateInterval: number;
  
  // Actions
  setActiveTab: (tab: string) => void;
  setSelectedAgent: (agentId: string | null) => void;
  setSelectedTask: (taskId: string | null) => void;
  
  // Data fetching
  fetchAgents: () => Promise<void>;
  fetchTasks: () => Promise<void>;
  fetchSystemMetrics: () => Promise<void>;
  fetchNetworkStatus: () => Promise<void>;
  refreshAll: () => Promise<void>;
  
  // Agent management
  createAgent: (agent: Omit<Agent, 'id' | 'lastActivity'>) => Promise<void>;
  updateAgent: (agentId: string, updates: Partial<Agent>) => Promise<void>;
  deleteAgent: (agentId: string) => Promise<void>;
  
  // Task management
  createTask: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (taskId: string) => Promise<void>;
  
  // Real-time updates
  startRealTimeUpdates: () => void;
  stopRealTimeUpdates: () => void;
}

// Default data for initial state
const defaultAgents: Agent[] = [
  {
    id: 'agent-1',
    name: 'Research Agent',
    type: 'ai',
    status: 'active',
    tasks: 3,
    performance: 94,
    capabilities: ['research', 'analysis', 'data-gathering'],
    lastActivity: new Date(),
    version: '1.2.0'
  },
  {
    id: 'agent-2',
    name: 'Writing Agent',
    type: 'ai',
    status: 'idle',
    tasks: 0,
    performance: 87,
    capabilities: ['content-generation', 'editing', 'summarization'],
    lastActivity: new Date(Date.now() - 300000), // 5 minutes ago
    version: '1.1.0'
  },
  {
    id: 'agent-3',
    name: 'Analysis Agent',
    type: 'ai',
    status: 'busy',
    tasks: 1,
    performance: 91,
    capabilities: ['data-analysis', 'pattern-recognition', 'insights'],
    lastActivity: new Date(),
    version: '1.3.0'
  },
  {
    id: 'agent-4',
    name: 'Code Agent',
    type: 'ai',
    status: 'active',
    tasks: 2,
    performance: 96,
    capabilities: ['code-generation', 'debugging', 'optimization'],
    lastActivity: new Date(),
    version: '2.0.0'
  }
];

const defaultTasks: Task[] = [
  {
    id: 'task-1',
    title: 'Research market trends',
    description: 'Analyze current market trends in AI technology',
    agentId: 'agent-1',
    agentName: 'Research Agent',
    priority: 'high',
    status: 'in-progress',
    createdAt: new Date(Date.now() - 3600000), // 1 hour ago
    updatedAt: new Date(),
    estimatedDuration: 7200000 // 2 hours
  },
  {
    id: 'task-2',
    title: 'Generate content outline',
    description: 'Create outline for technical documentation',
    agentId: 'agent-2',
    agentName: 'Writing Agent',
    priority: 'medium',
    status: 'queued',
    createdAt: new Date(Date.now() - 1800000), // 30 minutes ago
    updatedAt: new Date(Date.now() - 1800000),
    estimatedDuration: 3600000 // 1 hour
  },
  {
    id: 'task-3',
    title: 'Analyze user feedback',
    description: 'Process and analyze recent user feedback data',
    agentId: 'agent-3',
    agentName: 'Analysis Agent',
    priority: 'low',
    status: 'in-progress',
    createdAt: new Date(Date.now() - 900000), // 15 minutes ago
    updatedAt: new Date(),
    estimatedDuration: 1800000 // 30 minutes
  },
  {
    id: 'task-4',
    title: 'Code review automation',
    description: 'Implement automated code review system',
    agentId: 'agent-4',
    agentName: 'Code Agent',
    priority: 'high',
    status: 'completed',
    createdAt: new Date(Date.now() - 7200000), // 2 hours ago
    updatedAt: new Date(Date.now() - 600000), // 10 minutes ago
    estimatedDuration: 5400000, // 1.5 hours
    actualDuration: 4800000 // 1.33 hours
  }
];

const defaultSystemMetrics: SystemMetrics = {
  activeAgents: 4,
  totalAgents: 4,
  tasksRunning: 6,
  totalTasks: 12,
  successRate: 94,
  averageResponseTime: 1.2,
  systemHealth: 'healthy',
  uptime: 99.8
};

const defaultNetworkStatus: NetworkStatus = {
  serviceDiscovery: 'online',
  loadBalancer: 'healthy',
  messageQueue: 'warning',
  database: 'connected'
};

export const useOrchestrationStore = create<OrchestrationState>()(
  devtools(
    (set, get) => ({
      // Initial state
      agents: defaultAgents,
      tasks: defaultTasks,
      systemMetrics: defaultSystemMetrics,
      networkStatus: defaultNetworkStatus,
      
      activeTab: 'overview',
      selectedAgent: null,
      selectedTask: null,
      
      loading: {
        agents: false,
        tasks: false,
        metrics: false,
      },
      
      errors: {
        agents: null,
        tasks: null,
        metrics: null,
      },
      
      lastUpdated: new Date(),
      updateInterval: 30000, // 30 seconds
      
      // UI Actions
      setActiveTab: (tab) => set({ activeTab: tab }),
      setSelectedAgent: (agentId) => set({ selectedAgent: agentId }),
      setSelectedTask: (taskId) => set({ selectedTask: taskId }),
      
      // Data fetching (will be implemented with real APIs)
      fetchAgents: async () => {
        set((state) => ({ loading: { ...state.loading, agents: true } }));
        try {
          // TODO: Replace with real API call
          // const response = await fetch('/api/orchestration/agents');
          // const agents = await response.json();
          
          // For now, simulate API call with updated data
          await new Promise(resolve => setTimeout(resolve, 500));
          
          const updatedAgents = get().agents.map(agent => ({
            ...agent,
            lastActivity: new Date(),
            // Simulate some status changes
            status: Math.random() > 0.8 ? 
              (['active', 'idle', 'busy'] as const)[Math.floor(Math.random() * 3)] : 
              agent.status,
            performance: Math.max(80, Math.min(100, agent.performance + (Math.random() - 0.5) * 2))
          }));
          
          set((state) => ({
            agents: updatedAgents,
            loading: { ...state.loading, agents: false },
            errors: { ...state.errors, agents: null },
            lastUpdated: new Date()
          }));
        } catch (error) {
          set((state) => ({
            loading: { ...state.loading, agents: false },
            errors: { ...state.errors, agents: error instanceof Error ? error.message : 'Failed to fetch agents' }
          }));
        }
      },
      
      fetchTasks: async () => {
        set((state) => ({ loading: { ...state.loading, tasks: true } }));
        try {
          // TODO: Replace with real API call
          await new Promise(resolve => setTimeout(resolve, 300));
          
          // Simulate task updates
          const updatedTasks = get().tasks.map(task => ({
            ...task,
            updatedAt: new Date(),
            // Simulate some progress
            status: task.status === 'in-progress' && Math.random() > 0.7 ? 'completed' : task.status
          }));
          
          set((state) => ({
            tasks: updatedTasks,
            loading: { ...state.loading, tasks: false },
            errors: { ...state.errors, tasks: null },
            lastUpdated: new Date()
          }));
        } catch (error) {
          set((state) => ({
            loading: { ...state.loading, tasks: false },
            errors: { ...state.errors, tasks: error instanceof Error ? error.message : 'Failed to fetch tasks' }
          }));
        }
      },
      
      fetchSystemMetrics: async () => {
        set((state) => ({ loading: { ...state.loading, metrics: true } }));
        try {
          // TODO: Replace with real API call
          await new Promise(resolve => setTimeout(resolve, 200));
          
          const agents = get().agents;
          const tasks = get().tasks;
          
          const activeAgents = agents.filter(a => a.status === 'active' || a.status === 'busy').length;
          const runningTasks = tasks.filter(t => t.status === 'in-progress').length;
          const completedTasks = tasks.filter(t => t.status === 'completed').length;
          const successRate = tasks.length > 0 ? (completedTasks / tasks.length) * 100 : 0;
          
          const updatedMetrics: SystemMetrics = {
            activeAgents,
            totalAgents: agents.length,
            tasksRunning: runningTasks,
            totalTasks: tasks.length,
            successRate: Math.round(successRate),
            averageResponseTime: 1.2 + (Math.random() - 0.5) * 0.4,
            systemHealth: activeAgents === agents.length ? 'healthy' : 
                         activeAgents > agents.length * 0.7 ? 'warning' : 'critical',
            uptime: 99.8 + (Math.random() - 0.5) * 0.4
          };
          
          set((state) => ({
            systemMetrics: updatedMetrics,
            loading: { ...state.loading, metrics: false },
            errors: { ...state.errors, metrics: null },
            lastUpdated: new Date()
          }));
        } catch (error) {
          set((state) => ({
            loading: { ...state.loading, metrics: false },
            errors: { ...state.errors, metrics: error instanceof Error ? error.message : 'Failed to fetch metrics' }
          }));
        }
      },
      
      fetchNetworkStatus: async () => {
        try {
          // TODO: Replace with real API call
          await new Promise(resolve => setTimeout(resolve, 100));
          
          // Simulate network status updates
          const statuses = ['online', 'warning', 'offline'] as const;
          const healthStatuses = ['healthy', 'warning', 'critical'] as const;
          const dbStatuses = ['connected', 'slow', 'disconnected'] as const;
          
          const updatedStatus: NetworkStatus = {
            serviceDiscovery: Math.random() > 0.9 ? statuses[Math.floor(Math.random() * statuses.length)] : 'online',
            loadBalancer: Math.random() > 0.8 ? healthStatuses[Math.floor(Math.random() * healthStatuses.length)] : 'healthy',
            messageQueue: Math.random() > 0.7 ? statuses[Math.floor(Math.random() * statuses.length)] : 'warning',
            database: Math.random() > 0.95 ? dbStatuses[Math.floor(Math.random() * dbStatuses.length)] : 'connected'
          };
          
          set({ networkStatus: updatedStatus, lastUpdated: new Date() });
        } catch (error) {
          console.error('Failed to fetch network status:', error);
        }
      },
      
      refreshAll: async () => {
        const { fetchAgents, fetchTasks, fetchSystemMetrics, fetchNetworkStatus } = get();
        await Promise.all([
          fetchAgents(),
          fetchTasks(),
          fetchSystemMetrics(),
          fetchNetworkStatus()
        ]);
      },
      
      // Agent management (placeholder implementations)
      createAgent: async (agentData) => {
        const newAgent: Agent = {
          ...agentData,
          id: `agent-${Date.now()}`,
          lastActivity: new Date()
        };
        
        set((state) => ({
          agents: [...state.agents, newAgent],
          lastUpdated: new Date()
        }));
      },
      
      updateAgent: async (agentId, updates) => {
        set((state) => ({
          agents: state.agents.map(agent =>
            agent.id === agentId ? { ...agent, ...updates, lastActivity: new Date() } : agent
          ),
          lastUpdated: new Date()
        }));
      },
      
      deleteAgent: async (agentId) => {
        set((state) => ({
          agents: state.agents.filter(agent => agent.id !== agentId),
          selectedAgent: state.selectedAgent === agentId ? null : state.selectedAgent,
          lastUpdated: new Date()
        }));
      },
      
      // Task management (placeholder implementations)
      createTask: async (taskData) => {
        const newTask: Task = {
          ...taskData,
          id: `task-${Date.now()}`,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        set((state) => ({
          tasks: [...state.tasks, newTask],
          lastUpdated: new Date()
        }));
      },
      
      updateTask: async (taskId, updates) => {
        set((state) => ({
          tasks: state.tasks.map(task =>
            task.id === taskId ? { ...task, ...updates, updatedAt: new Date() } : task
          ),
          lastUpdated: new Date()
        }));
      },
      
      deleteTask: async (taskId) => {
        set((state) => ({
          tasks: state.tasks.filter(task => task.id !== taskId),
          selectedTask: state.selectedTask === taskId ? null : state.selectedTask,
          lastUpdated: new Date()
        }));
      },
      
      // Real-time updates
      startRealTimeUpdates: () => {
        const interval = setInterval(() => {
          const { refreshAll } = get();
          refreshAll();
        }, get().updateInterval);
        
        // Store interval ID for cleanup (would need to be added to state)
        (window as any).orchestrationInterval = interval;
      },
      
      stopRealTimeUpdates: () => {
        if ((window as any).orchestrationInterval) {
          clearInterval((window as any).orchestrationInterval);
          delete (window as any).orchestrationInterval;
        }
      }
    }),
    {
      name: 'orchestration-store'
    }
  )
);
