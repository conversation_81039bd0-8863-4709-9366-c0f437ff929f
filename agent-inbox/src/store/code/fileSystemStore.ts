import { create } from 'zustand';

export interface FileNode {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'folder';
  children?: FileNode[];
  expanded?: boolean;
  size?: number;
  lastModified?: Date;
}

export interface FileSystemState {
  // File tree
  rootNodes: FileNode[];
  expandedFolders: Set<string>;
  
  // Current working directory
  currentPath: string;
  
  // Actions
  loadFileTree: () => void;
  toggleFolder: (folderId: string) => void;
  createFile: (parentPath: string, fileName: string) => void;
  createFolder: (parentPath: string, folderName: string) => void;
  deleteNode: (nodePath: string) => void;
  renameNode: (nodePath: string, newName: string) => void;
  
  // File operations
  readFile: (filePath: string) => Promise<string>;
  writeFile: (filePath: string, content: string) => Promise<void>;
  
  // Utilities
  getNodeByPath: (path: string) => FileNode | null;
  getLanguageFromPath: (path: string) => string;
}

// Mock file system data for demonstration
const createMockFileTree = (): FileNode[] => [
  {
    id: 'src',
    name: 'src',
    path: 'src',
    type: 'folder',
    expanded: true,
    children: [
      {
        id: 'src/components',
        name: 'components',
        path: 'src/components',
        type: 'folder',
        expanded: true,
        children: [
          {
            id: 'src/components/Button.tsx',
            name: 'Button.tsx',
            path: 'src/components/Button.tsx',
            type: 'file',
            size: 1024,
            lastModified: new Date(),
          },
          {
            id: 'src/components/Input.tsx',
            name: 'Input.tsx',
            path: 'src/components/Input.tsx',
            type: 'file',
            size: 856,
            lastModified: new Date(),
          },
        ],
      },
      {
        id: 'src/pages',
        name: 'pages',
        path: 'src/pages',
        type: 'folder',
        children: [
          {
            id: 'src/pages/index.tsx',
            name: 'index.tsx',
            path: 'src/pages/index.tsx',
            type: 'file',
            size: 2048,
            lastModified: new Date(),
          },
        ],
      },
      {
        id: 'src/utils',
        name: 'utils',
        path: 'src/utils',
        type: 'folder',
        children: [
          {
            id: 'src/utils/helpers.ts',
            name: 'helpers.ts',
            path: 'src/utils/helpers.ts',
            type: 'file',
            size: 512,
            lastModified: new Date(),
          },
        ],
      },
    ],
  },
  {
    id: 'package.json',
    name: 'package.json',
    path: 'package.json',
    type: 'file',
    size: 1536,
    lastModified: new Date(),
  },
  {
    id: 'README.md',
    name: 'README.md',
    path: 'README.md',
    type: 'file',
    size: 2048,
    lastModified: new Date(),
  },
  {
    id: 'tsconfig.json',
    name: 'tsconfig.json',
    path: 'tsconfig.json',
    type: 'file',
    size: 768,
    lastModified: new Date(),
  },
];

// Mock file contents
const mockFileContents: Record<string, string> = {
  'src/components/Button.tsx': `import React from 'react';
import { cn } from '@/lib/utils';

interface ButtonProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
}

export function Button({ 
  children, 
  className, 
  onClick, 
  variant = 'primary',
  size = 'md',
  disabled = false 
}: ButtonProps) {
  return (
    <button
      className={cn(
        "px-4 py-2 rounded-lg font-medium transition-colors",
        {
          'bg-blue-600 text-white hover:bg-blue-700': variant === 'primary',
          'bg-gray-600 text-white hover:bg-gray-700': variant === 'secondary',
          'border border-gray-300 hover:bg-gray-50': variant === 'outline',
          'px-2 py-1 text-sm': size === 'sm',
          'px-4 py-2': size === 'md',
          'px-6 py-3 text-lg': size === 'lg',
          'opacity-50 cursor-not-allowed': disabled,
        },
        className
      )}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}`,
  'src/components/Input.tsx': `import React from 'react';
import { cn } from '@/lib/utils';

interface InputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  type?: 'text' | 'email' | 'password' | 'number';
  disabled?: boolean;
}

export function Input({ 
  value, 
  onChange, 
  placeholder, 
  className, 
  type = 'text',
  disabled = false 
}: InputProps) {
  return (
    <input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      className={cn(
        "px-3 py-2 border border-gray-300 rounded-lg",
        "focus:outline-none focus:ring-2 focus:ring-blue-500",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        className
      )}
    />
  );
}`,
  'src/pages/index.tsx': `import React from 'react';
import { Button } from '@/components/Button';
import { Input } from '@/components/Input';

export default function HomePage() {
  const [inputValue, setInputValue] = React.useState('');

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6">Welcome</h1>
        
        <div className="space-y-4">
          <Input
            value={inputValue}
            onChange={setInputValue}
            placeholder="Enter some text..."
          />
          
          <Button onClick={() => alert('Hello!')}>
            Click me
          </Button>
        </div>
      </div>
    </div>
  );
}`,
  'package.json': `{
  "name": "my-app",
  "version": "1.0.0",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start"
  },
  "dependencies": {
    "react": "^18.0.0",
    "next": "^14.0.0"
  }
}`,
  'README.md': `# My App

This is a sample application built with React and Next.js.

## Getting Started

\`\`\`bash
npm run dev
\`\`\`

## Features

- Modern React components
- TypeScript support
- Tailwind CSS styling
`,
};

export const useFileSystemStore = create<FileSystemState>((set, get) => ({
  // Initial state
  rootNodes: [],
  expandedFolders: new Set(['src', 'src/components']),
  currentPath: '/',

  // Actions
  loadFileTree: () => {
    set({ rootNodes: createMockFileTree() });
  },

  toggleFolder: (folderId) => {
    set((state) => {
      const newExpanded = new Set(state.expandedFolders);
      if (newExpanded.has(folderId)) {
        newExpanded.delete(folderId);
      } else {
        newExpanded.add(folderId);
      }
      return { expandedFolders: newExpanded };
    });
  },

  createFile: (parentPath, fileName) => {
    // Mock implementation
    console.log(`Creating file: ${parentPath}/${fileName}`);
  },

  createFolder: (parentPath, folderName) => {
    // Mock implementation
    console.log(`Creating folder: ${parentPath}/${folderName}`);
  },

  deleteNode: (nodePath) => {
    // Mock implementation
    console.log(`Deleting: ${nodePath}`);
  },

  renameNode: (nodePath, newName) => {
    // Mock implementation
    console.log(`Renaming ${nodePath} to ${newName}`);
  },

  // File operations
  readFile: async (filePath) => {
    // Mock file reading
    return mockFileContents[filePath] || `// File: ${filePath}\n// Content not available`;
  },

  writeFile: async (filePath, content) => {
    // Mock file writing
    mockFileContents[filePath] = content;
    console.log(`Saved file: ${filePath}`);
  },

  // Utilities
  getNodeByPath: (path) => {
    const findNode = (nodes: FileNode[], targetPath: string): FileNode | null => {
      for (const node of nodes) {
        if (node.path === targetPath) return node;
        if (node.children) {
          const found = findNode(node.children, targetPath);
          if (found) return found;
        }
      }
      return null;
    };
    return findNode(get().rootNodes, path);
  },

  getLanguageFromPath: (path) => {
    const ext = path.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'json': 'json',
      'md': 'markdown',
      'css': 'css',
      'scss': 'scss',
      'html': 'html',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'php': 'php',
      'sql': 'sql',
      'xml': 'xml',
    };
    return languageMap[ext || ''] || 'plaintext';
  },
}));
