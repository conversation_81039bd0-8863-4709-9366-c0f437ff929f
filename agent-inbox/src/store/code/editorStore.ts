import { create } from 'zustand';

export interface EditorFile {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  isDirty: boolean;
  isActive: boolean;
}

export interface EditorSettings {
  theme: 'vs-dark' | 'vs-light' | 'hc-black';
  fontSize: number;
  wordWrap: 'on' | 'off' | 'wordWrapColumn' | 'bounded';
  minimap: boolean;
  lineNumbers: 'on' | 'off' | 'relative' | 'interval';
}

export interface EditorState {
  // Files and tabs
  openFiles: EditorFile[];
  activeFileId: string | null;
  
  // Editor settings
  settings: EditorSettings;
  
  // Editor state
  cursorPosition: { line: number; column: number };
  
  // Actions
  openFile: (file: Omit<EditorFile, 'id' | 'isDirty' | 'isActive'>) => void;
  closeFile: (fileId: string) => void;
  setActiveFile: (fileId: string) => void;
  updateFileContent: (fileId: string, content: string) => void;
  saveFile: (fileId: string) => void;
  updateSettings: (settings: Partial<EditorSettings>) => void;
  setCursorPosition: (position: { line: number; column: number }) => void;
  
  // Getters
  getActiveFile: () => EditorFile | null;
  getFileById: (fileId: string) => EditorFile | null;
}

export const useEditorStore = create<EditorState>((set, get) => ({
  // Initial state
  openFiles: [],
  activeFileId: null,
  settings: {
    theme: 'vs-dark',
    fontSize: 14,
    wordWrap: 'on',
    minimap: true,
    lineNumbers: 'on',
  },
  cursorPosition: { line: 1, column: 1 },

  // Actions
  openFile: (fileData) => {
    const id = `${fileData.path}-${Date.now()}`;
    const newFile: EditorFile = {
      ...fileData,
      id,
      isDirty: false,
      isActive: true,
    };

    set((state) => {
      // Check if file is already open
      const existingFile = state.openFiles.find(f => f.path === fileData.path);
      if (existingFile) {
        return {
          ...state,
          activeFileId: existingFile.id,
          openFiles: state.openFiles.map(f => ({
            ...f,
            isActive: f.id === existingFile.id
          }))
        };
      }

      // Add new file
      return {
        ...state,
        openFiles: [
          ...state.openFiles.map(f => ({ ...f, isActive: false })),
          newFile
        ],
        activeFileId: id,
      };
    });
  },

  closeFile: (fileId) => {
    set((state) => {
      const newOpenFiles = state.openFiles.filter(f => f.id !== fileId);
      let newActiveFileId = state.activeFileId;

      // If closing active file, switch to another file
      if (state.activeFileId === fileId) {
        if (newOpenFiles.length > 0) {
          newActiveFileId = newOpenFiles[newOpenFiles.length - 1].id;
          newOpenFiles[newOpenFiles.length - 1].isActive = true;
        } else {
          newActiveFileId = null;
        }
      }

      return {
        ...state,
        openFiles: newOpenFiles,
        activeFileId: newActiveFileId,
      };
    });
  },

  setActiveFile: (fileId) => {
    set((state) => ({
      ...state,
      activeFileId: fileId,
      openFiles: state.openFiles.map(f => ({
        ...f,
        isActive: f.id === fileId
      }))
    }));
  },

  updateFileContent: (fileId, content) => {
    set((state) => ({
      ...state,
      openFiles: state.openFiles.map(f =>
        f.id === fileId
          ? { ...f, content, isDirty: true }
          : f
      )
    }));
  },

  saveFile: (fileId) => {
    set((state) => ({
      ...state,
      openFiles: state.openFiles.map(f =>
        f.id === fileId
          ? { ...f, isDirty: false }
          : f
      )
    }));
  },

  updateSettings: (newSettings) => {
    set((state) => ({
      ...state,
      settings: { ...state.settings, ...newSettings }
    }));
  },

  setCursorPosition: (position) => {
    set({ cursorPosition: position });
  },

  // Getters
  getActiveFile: () => {
    const state = get();
    return state.openFiles.find(f => f.id === state.activeFileId) || null;
  },

  getFileById: (fileId) => {
    const state = get();
    return state.openFiles.find(f => f.id === fileId) || null;
  },
}));
