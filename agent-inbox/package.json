{"name": "agent-inbox", "author": "<PERSON><PERSON>", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\""}, "dependencies": {"@assistant-ui/react": "^0.5.71", "@assistant-ui/react-markdown": "^0.2.18", "@assistant-ui/react-syntax-highlighter": "^0.0.13", "@blocknote/core": "^0.17.1", "@blocknote/mantine": "^0.17.1", "@blocknote/react": "^0.17.1", "@blocknote/shadcn": "^0.17.1", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.1.6", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/lang-xml": "^6.1.0", "@faker-js/faker": "^9.2.0", "@langchain/anthropic": "^0.3.6", "@langchain/community": "^0.3.9", "@langchain/core": "^0.3.14", "@langchain/google-genai": "^0.1.2", "@langchain/langgraph": "^0.2.23", "@langchain/langgraph-cua": "^0.0.5", "@langchain/langgraph-sdk": "^0.0.61", "@langchain/openai": "^0.3.11", "@monaco-editor/react": "^4.7.0", "@nextjournal/lang-clojure": "^1.0.0", "@pixi/react": "^8.0.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.4", "@replit/codemirror-lang-csharp": "^6.2.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.5", "@tanstack/react-table": "^8.20.5", "@types/fabric": "^5.3.10", "@types/file-saver": "^2.0.7", "@types/pixi.js": "^4.8.9", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/react-codemirror": "^4.23.5", "@uiw/react-md-editor": "^4.0.4", "@vercel/kv": "^2.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "eslint-plugin-unused-imports": "^4.1.4", "fabric": "^6.7.0", "file-saver": "^2.0.5", "framer-motion": "^11.11.9", "js-cookie": "^3.0.5", "langchain": "^0.3.5", "langsmith": "^0.1.61", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "monaco-editor": "^0.52.2", "next": "14.2.25", "pixi.js": "^8.11.0", "react": "^18", "react-colorful": "^5.6.1", "react-dom": "^18", "react-icons": "^5.3.0", "react-json-view": "^1.21.3", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "react-twitter-embed": "^4.0.4", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "replicate": "^1.0.1", "tailwind-merge": "^2.5.2", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "twitter-api-v2": "^1.24.0", "uuid": "^11.1.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.12.0", "@types/eslint__js": "^8.42.3", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.8.1", "eslint": "^8", "eslint-config-next": "14.2.10", "postcss": "^8", "prettier": "^3.3.3", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "^5", "typescript-eslint": "^8.8.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}